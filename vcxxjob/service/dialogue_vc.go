package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/database"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/httpcli"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/event"
	"new-gitlab.xunlei.cn/vcproject/backends/vcxxjob/model"
)

var (
	ProdTTSUrl                = "http://**************:50002/v1/tts"
	TestTTSUrl                = "http://**************:50007/v1/tts"
	RVCDomain                 = "https://rvc.character.xunlei.com/"
	TestRVCDomain             = "https://rvc-test.character.xunlei.com/"
	ProdFishSpeechTTSUrl      = "http://**************:50002/v1/fish_speech_tts"
	TestFishSpeechTTSUrl      = "http://**************:50007/v1/fish_speech_tts"
	AsyncTTSUrl               = "http://**************:50002/v1/tts_async"
	TestAsyncTTSUrl           = "http://**************:50007/v1/tts_async"
	AsyncFishSpeechTTSUrl     = "http://**************:50002/v1/fish_speech_tts_async"
	TestAsyncFishSpeechTTSUrl = "http://**************:50007/v1/fish_speech_tts_async"
)

type DubbingExt struct {
	LineId           int64 `json:"line_id"`
	CharacterID      int64 `json:"character_id"`
	CharacterAssetID int64 `json:"character_asset_id"`
	ScriptID         int64 `json:"script_id"`
}

type TTSReq struct {
	ReferAudioUrl string `json:"refer_audio_url"`
	PromptText    string `json:"prompt_text"`
	Text          string `json:"text"`
}

type TTSResp struct {
	Code int32        `json:"code"`
	Msg  string       `json:"msg"`
	Data *TTSRespData `json:"data"`
}

type TTSRespData struct {
	Url      string `json:"url"`
	Duration int64  `json:"duration"`
}

type AsyncTTSReq struct {
	ReferAudioUrl string `json:"refer_audio_url"`
	PromptText    string `json:"prompt_text"`
	Text          string `json:"text"`
	TaskId        string `json:"task_id"`
}
type AsyncTTSResp struct {
	Code int32             `json:"code"`
	Msg  string            `json:"msg"`
	Data *AsyncTTSRespData `json:"data"`
}

type AsyncTTSRespData struct {
	TaskId string `json:"task_id"`
}

type VoicePresetResp struct {
	Ret  int32                       `json:"ret"`
	Data map[string]*VoicePresetItem `json:"data"`
}

type VoicePresetItem struct {
	PresetName        string  `json:"preset_name"`
	RVCName           string  `json:"rvc_name"`
	RVCModelURL       string  `json:"rvc_model_url"`
	RVCIndexURL       string  `json:"rvc_index_url"`
	SeedName          string  `json:"seed_name"`
	ReferenceAudioURL string  `json:"reference_audio_url"`
	F0Method          string  `json:"f0_method"`
	F0UpKey           float64 `json:"f0_up_key"`
	Protect           float64 `json:"protect"`
	RMSMixRate        float64 `json:"rms_mix_rate"`
	DiffusionSteps    int     `json:"diffusion_steps"`
	CascadedUseRVC    bool    `json:"cascaded_use_rvc"`
	F0Condition       bool    `json:"f0_condition"`
	AutoF0Adjust      bool    `json:"auto_f0_adjust"`
	SemiToneShift     int     `json:"semi_tone_shift"`
}

// 单个preset返回
type VoicePresetByNameResp struct {
	Ret  int32            `json:"ret"`
	Msg  string           `json:"msg"`
	Data *VoicePresetItem `json:"data"`
}

type LoadModelReq struct {
	RvcName       string `json:"rvc_name"`
	SeedName      string `json:"seed_name"`
	ReferAudioUrl string `json:"refer_audio_url"`
	RequestId     string `json:"request_id"`
	Mid           string `json:"mid"`
}

type LoadModelResp struct {
	Ret int32  `json:"ret"`
	Msg string `json:"msg"`
}

type InferRvcReq struct {
	RvcName           string `json:"rvc_name"`
	SeedName          string `json:"seed_name"`
	ReferenceAudioUrl string `json:"reference_audio_url"`
	RequestId         string `json:"request_id"`
	Mid               string `json:"mid"`
	SourceAudioUrl    string `json:"source_audio_url"`
	CascadedUseRvc    bool   `json:"cascaded_use_rvc"`
}

type InferRvcResp struct {
	Ret  int32             `json:"ret"`
	Msg  string            `json:"msg"`
	Data *InferRvcRespData `json:"data"`
}

type InferRvcRespData struct {
	RequestId           string `json:"request_id"`
	SeedOutputUrl       string `json:"seed_output_url"`
	RvcOutputUrl        string `json:"rvc_output_url"`
	FinalOutputUrl      string `json:"final_output_url"`
	FinalOutputDuration int64  `json:"final_output_duration"`
}

type DialogueVCService struct {
	httpClient     *httpcli.HTTPClient
	vcTaskInstance model.VcTaskModelInterface
}

var (
	dialogueVCService *DialogueVCService
	once              sync.Once
)

// GetSpeechToTextService 获取单例实例
func GetDialogueVCService() *DialogueVCService {
	once.Do(func() {
		dialogueVCService = newDialogueVcService()
	})
	return dialogueVCService
}

func newDialogueVcService() *DialogueVCService {
	poolConfig := httpcli.PoolConfig{
		MaxSize:         200,
		InitialSize:     20,
		ConnTimeout:     300 * time.Second,
		IdleTimeout:     60 * time.Second,
		MaxConnsPerHost: 30,
	}
	// 创建HTTP客户端
	client, err := httpcli.NewHTTPClient(&poolConfig)
	if err != nil {
		logger.Panicf("newHttpClient err=%+v", err)
	}
	vcTaskInstance := model.NewVcTaskModel(model.NewBaseModel(database.GetMysqlDB("vc_misc")))
	return &DialogueVCService{
		httpClient:     client,
		vcTaskInstance: vcTaskInstance,
	}
}

func getTTSUrl(ttsEngine string) string {
	lowerTTSEngine := strings.ToLower(ttsEngine)
	if env.IsProd() {
		if lowerTTSEngine == "fish_speech" {
			return ProdFishSpeechTTSUrl
		}
		return ProdTTSUrl
	}
	if lowerTTSEngine == "fish_speech" {
		return TestFishSpeechTTSUrl
	}
	return TestTTSUrl
}

func getAsyncTTSUrl(ttsEngine string) string {
	lowerTTSEngine := strings.ToLower(ttsEngine)
	if env.IsProd() {
		if lowerTTSEngine == "fish_speech" {
			return AsyncFishSpeechTTSUrl
		}
		return AsyncTTSUrl
	}
	if lowerTTSEngine == "fish_speech" {
		return TestAsyncFishSpeechTTSUrl
	}
	return TestAsyncTTSUrl
}

func (s *DialogueVCService) TextToSpeech(ctx context.Context, req *TTSReq, ttsEngine string) (resp *TTSResp, err error) {
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	data := map[string]interface{}{
		"refer_audio_url": req.ReferAudioUrl,
		"prompt_text":     req.PromptText,
		"text":            req.Text,
	}
	requestUrl := getTTSUrl(ttsEngine)
	logger.Infof("url=%s, ttsEngine=%s headers=%+v, data=%+v", requestUrl, ttsEngine, headers, util.JsonStr(data))
	postResp, tmpErr := s.httpClient.Post(ctx, requestUrl, headers, data, 600*time.Second)
	if tmpErr != nil {
		err = tmpErr
		logger.Errorf("tmpErr =%+v requestUrl=%s", tmpErr, requestUrl)
		return
	}
	if postResp.StatusCode != http.StatusOK {
		logger.Errorf("postResp.StatusCode =%d, postResp.Body =%s", postResp.StatusCode, postResp.Body)
		err = fmt.Errorf("httpCode = %d", postResp.StatusCode)
		return
	}
	logger.Debugf("postResp.Body=%+v reqData=%s", string(postResp.Body), util.JsonStr(data))
	resp = &TTSResp{}
	if tmpErr := json.Unmarshal(postResp.Body, resp); tmpErr != nil {
		logger.Errorf("json.Unmarshal err=%+v body=%s", tmpErr, string(postResp.Body))
		err = tmpErr
		return
	}
	return
}

func (s *DialogueVCService) AsyncTextToSpeech(ctx context.Context, req *AsyncTTSReq, ttsEngine string) (resp *AsyncTTSResp, err error) {
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	data := map[string]interface{}{
		"refer_audio_url": req.ReferAudioUrl,
		"prompt_text":     req.PromptText,
		"text":            req.Text,
		"task_id":         req.TaskId,
	}
	requestUrl := getAsyncTTSUrl(ttsEngine)
	logger.Infof("url=%s, ttsEngine=%s headers=%+v, data=%+v", requestUrl, ttsEngine, headers, util.JsonStr(data))
	postResp, tmpErr := s.httpClient.Post(ctx, requestUrl, headers, data, 600*time.Second)
	if tmpErr != nil {
		err = tmpErr
		logger.Errorf("tmpErr =%+v requestUrl=%s", tmpErr, requestUrl)
		return
	}
	if postResp.StatusCode != http.StatusOK {
		logger.Errorf("postResp.StatusCode =%d, postResp.Body =%s", postResp.StatusCode, postResp.Body)
		err = fmt.Errorf("httpCode = %d", postResp.StatusCode)
		return
	}
	logger.Debugf("postResp.Body=%+v reqData=%s", string(postResp.Body), util.JsonStr(data))
	resp = &AsyncTTSResp{}
	if tmpErr := json.Unmarshal(postResp.Body, resp); tmpErr != nil {
		logger.Errorf("json.Unmarshal err=%+v body=%s", tmpErr, string(postResp.Body))
		err = tmpErr
		return
	}
	return
}

func (s *DialogueVCService) GetPresets(ctx context.Context) (resp *VoicePresetResp, err error) {
	environment := "prod"
	if env.IsTest() {
		environment = "test"
	}
	headers := map[string]string{
		"Content-Type": "application/json",
		"env":          environment,
	}
	requestUrl := TestRVCDomain + "v1/cascaded_vc/get_presets"
	if env.IsProd() {
		requestUrl = RVCDomain + "v1/cascaded_vc/get_presets"
	}
	logger.Infof("url=%s, headers=%+v", requestUrl, headers)
	resp = &VoicePresetResp{}
	presetResp, tmpErr := s.httpClient.Get(ctx, requestUrl, headers, 60*time.Second)
	if tmpErr != nil {
		err = tmpErr
		logger.Errorf("tmpErr =%+v", tmpErr)
		return
	}
	if presetResp.StatusCode != http.StatusOK {
		logger.Errorf("postResp.StatusCode =%d, postResp.Body =%s", presetResp.StatusCode, presetResp.Body)
		err = fmt.Errorf("httpCode = %d", presetResp.StatusCode)
		return
	}
	logger.Debugf("postResp.Body=%+v reqHeader=%+v", string(presetResp.Body), util.JsonStr(headers))
	if tmpErr := json.Unmarshal(presetResp.Body, resp); tmpErr != nil {
		logger.Errorf("json.Unmarshal err=%+v body=%s", tmpErr, string(presetResp.Body))
		err = tmpErr
		return
	}
	return
}

func (s *DialogueVCService) GetPresetByName(ctx context.Context, presetName string) (resp *VoicePresetByNameResp, err error) {
	environment := "prod"
	if env.IsTest() {
		environment = "test"
	}
	headers := map[string]string{
		"Content-Type": "application/json",
		"env":          environment,
		"preset_name":  presetName,
	}
	requestUrl := TestRVCDomain + "v1/cascaded_vc/preset"
	if env.IsProd() {
		requestUrl = RVCDomain + "v1/cascaded_vc/preset"
	}
	logger.Infof("url=%s, headers=%+v", requestUrl, headers)
	resp = &VoicePresetByNameResp{}
	presetResp, tmpErr := s.httpClient.Get(ctx, requestUrl, headers, 60*time.Second)
	if tmpErr != nil {
		err = tmpErr
		logger.Errorf("tmpErr =%+v", tmpErr)
		return
	}
	if presetResp.StatusCode != http.StatusOK {
		logger.Errorf("postResp.StatusCode =%d, postResp.Body =%s", presetResp.StatusCode, presetResp.Body)
		err = fmt.Errorf("httpCode = %d", presetResp.StatusCode)
		return
	}
	logger.Debugf("postResp.Body=%+v reqHeader=%+v", string(presetResp.Body), util.JsonStr(headers))
	if tmpErr := json.Unmarshal(presetResp.Body, resp); tmpErr != nil {
		logger.Errorf("json.Unmarshal err=%+v body=%s", tmpErr, string(presetResp.Body))
		err = tmpErr
		return
	}
	return
}

func (s *DialogueVCService) LoadModel(ctx context.Context, req *LoadModelReq) (resp *LoadModelResp, err error) {
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	data := map[string]interface{}{
		"rvc_name":        req.RvcName,
		"refer_audio_url": req.ReferAudioUrl,
		"seed_name":       req.SeedName,
		"request_id":      req.RequestId,
		"mid":             req.Mid,
	}
	requestUrl := TestRVCDomain + "v1/cascaded_vc/load_model"
	if env.IsProd() {
		requestUrl = RVCDomain + "v1/cascaded_vc/load_model"
	}
	resp = &LoadModelResp{}
	logger.Infof("url=%s, headers=%+v, data=%+v", requestUrl, headers, util.JsonStr(data))
	loadModelResp, tmpErr := s.httpClient.Post(ctx, requestUrl, headers, data, 60*time.Second)
	if tmpErr != nil {
		err = tmpErr
		logger.Errorf("tmpErr =%+v", tmpErr)
		return
	}
	if loadModelResp.StatusCode != http.StatusOK {
		logger.Errorf("postResp.StatusCode =%d, postResp.Body =%s", loadModelResp.StatusCode, loadModelResp.Body)
		err = fmt.Errorf("httpCode = %d", loadModelResp.StatusCode)
		return
	}
	logger.Debugf("postResp.Body=%+v reqBody=%s", string(loadModelResp.Body), util.JsonStr(data))
	if tmpErr := json.Unmarshal(loadModelResp.Body, resp); tmpErr != nil {
		logger.Errorf("json.Unmarshal err=%+v body=%s", tmpErr, string(loadModelResp.Body))
		err = tmpErr
		return
	}
	return
}

func (s *DialogueVCService) InferVc(ctx context.Context, req *InferRvcReq) (resp *InferRvcResp, err error) {
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	data := map[string]interface{}{
		"rvc_name":            req.RvcName,
		"reference_audio_url": req.ReferenceAudioUrl,
		"seed_name":           req.SeedName,
		"request_id":          req.RequestId,
		"mid":                 req.Mid,
		"source_audio_url":    req.SourceAudioUrl,
		"cascaded_use_rvc":    req.CascadedUseRvc,
	}
	requestUrl := TestRVCDomain + "v1/cascaded_vc/infer_cascaded_vc"
	if env.IsProd() {
		requestUrl = RVCDomain + "v1/cascaded_vc/infer_cascaded_vc"
	}
	resp = &InferRvcResp{}
	logger.Infof("url=%s, headers=%+v, data=%+v", requestUrl, headers, util.JsonStr(data))
	inferRevcResp, tmpErr := s.httpClient.Post(ctx, requestUrl, headers, data, 300*time.Second)
	if tmpErr != nil {
		err = tmpErr
		logger.Errorf("tmpErr =%+v", tmpErr)
		return
	}
	if inferRevcResp.StatusCode != http.StatusOK {
		logger.Errorf("postResp.StatusCode =%d, postResp.Body =%s", inferRevcResp.StatusCode, inferRevcResp.Body)
		err = fmt.Errorf("httpCode = %d", inferRevcResp.StatusCode)
		return
	}
	logger.Debugf("postResp.Body=%+v reqBody=%+v", string(inferRevcResp.Body), util.JsonStr(data))
	if tmpErr := json.Unmarshal(inferRevcResp.Body, resp); tmpErr != nil {
		logger.Errorf("json.Unmarshal err=%+v body=%s", tmpErr, string(inferRevcResp.Body))
		err = tmpErr
		return
	}
	return
}

func (s *DialogueVCService) CreateVcTask(ctx context.Context, req *vcxxjob.AsyncTTSReq) (resp *vcxxjob.AsyncTTSResp, err error) {
	resp = &vcxxjob.AsyncTTSResp{Base: errcode.ErrOK.ToSvcBaseResp()}
	logger.Infof("CreateVcTask req=%+v", util.JsonStr(req))
	lineId := int64(0)
	var (
		dubbingExt *DubbingExt = nil
	)
	if len(req.Ext) > 0 && req.Source == 0 {
		if tmpErr := json.Unmarshal([]byte(req.Ext), &dubbingExt); tmpErr != nil {
			logger.Errorf("json.Unmarshal err=%+v", tmpErr)
			resp.Base = errcode.ErrorParam.ToSvcBaseResp()
			return resp, nil
		}
		logger.Infof("dubbingExt=%+v", util.JsonStr(dubbingExt))
		lineId = dubbingExt.LineId
		if lineId > 0 && len(req.OutId) == 0 {
			req.OutId = fmt.Sprintf("%d", lineId)
		}
	}
	cleanedLineText := s.removeNarration(req.Text)
	logger.Debugf("CreateVcTask cleanedLineText=%v", cleanedLineText)
	if len(cleanedLineText) == 0 {
		outputData := event.TTSLineOutputData{
			LineID:          lineId,
			DubbingDuration: 0,
			DubbingURL:      "",
		}
		logger.Debugf("CreateVcTask outputData=%+v", util.JsonStr(outputData))
		//metric.ReportPv("dialogue_vc_succeed", 1, prometheus.Labels{})
		tmpErr := event.PushTTSLineOutput(outputData)
		if tmpErr != nil {
			logger.Errorf("PushTTSLineOutput err=%+v outputData=%+v", tmpErr, outputData)
		}
		// push消息队列
		resp.Data = &vcxxjob.AsyncTTSRespData{
			TaskId: "",
		}
		return
	}
	if len(req.OutId) > 0 {
		vcTask, tmpErr := s.vcTaskInstance.GetVcTaskByOutIDAndSource(ctx, req.OutId, int(req.Source))
		if tmpErr != nil && tmpErr != gorm.ErrRecordNotFound {
			logger.Errorf("GetVcTaskByOutIDAndSource err=%+v", tmpErr)
			resp.Base = errcode.ErrDBQueryFailed.ToSvcBaseResp()
			return resp, nil
		}
		logger.Debugf("vcTask=%+v", util.JsonStr(vcTask))
		if vcTask != nil {
			if vcTask.Status < vcxxjob.VcTaskStatus_STATUS_COMPLETED {
				resp.Data = &vcxxjob.AsyncTTSRespData{
					TaskId: vcTask.TaskId,
				}
				return
			} else { //已经成功或是失败
				tmpErr := s.vcTaskInstance.UpdateVcTaskByTaskId(ctx, vcTask.TaskId, map[string]interface{}{
					"status":               vcxxjob.VcTaskStatus_STATUS_UNSPECIFIED,
					"tts_audio_url":        "",
					"tts_audio_duration":   0,
					"final_audio_url":      "",
					"final_audio_duration": 0,
				})
				if tmpErr != nil {
					logger.Errorf("UpdateVcTaskByTaskId err=%+v", tmpErr)
					resp.Base = errcode.ErrDBUpdateFailed.ToSvcBaseResp()
					return resp, nil
				}
				ttsInputData := event.TTSLineInputData{
					ReferAudioUrl: req.ReferAudioUrl,
					PromptText:    req.ReferAudioText,
					LineText:      cleanedLineText,
					UseVc:         req.UseVc,
					UseRvc:        req.UseRvc,
					TTSEngine:     req.TtsEngine,
					TaskId:        vcTask.TaskId,
				}
				if dubbingExt != nil {
					ttsInputData.LineID = dubbingExt.LineId
					ttsInputData.CharacterID = dubbingExt.CharacterID
					ttsInputData.CharacterAssetID = dubbingExt.CharacterAssetID
					ttsInputData.ScriptID = dubbingExt.ScriptID
				}
				err = event.PushTTSLineInput(ttsInputData)
				if err != nil {
					logger.Errorf("PushTTSLineInput err=%+v", err)
					errcode.ErrorInternal.SetTo(resp)
					return resp, nil
				}
				resp.Data = &vcxxjob.AsyncTTSRespData{
					TaskId: vcTask.TaskId,
				}
				return
			}
		}
	}

	taskId := util.UUID()
	if len(req.OutId) == 0 {
		req.OutId = taskId
	}
	vcModel := &model.VcTask{
		TaskId:         taskId,
		OutId:          req.OutId,
		Source:         int(req.Source),
		ReferAudioUrl:  req.ReferAudioUrl,
		ReferAudioText: req.ReferAudioText,
		Content:        req.Text,
		TTSEngine:      req.TtsEngine,
		Ext:            req.Ext,
		Status:         vcxxjob.VcTaskStatus_STATUS_PROCESSING,
	}
	if req.UseVc {
		vcModel.UseVc = 1
	}
	if req.UseRvc {
		vcModel.UseRvc = 1
	}
	logger.Debugf("CreateVcTask vcModel=%+v", util.JsonStr(vcModel))
	if err = s.vcTaskInstance.CreateVcTask(ctx, vcModel); err != nil {
		logger.Errorf("CreateVcTask err=%+v", err)
		resp.Base = errcode.ErrDBInsertFailed.ToSvcBaseResp()
		return resp, nil
	}
	ttsInputData := event.TTSLineInputData{
		ReferAudioUrl: req.ReferAudioUrl,
		PromptText:    req.ReferAudioText,
		LineText:      cleanedLineText,
		UseVc:         req.UseVc,
		UseRvc:        req.UseRvc,
		TTSEngine:     req.TtsEngine,
		TaskId:        taskId,
	}
	if dubbingExt != nil {
		ttsInputData.LineID = dubbingExt.LineId
		ttsInputData.CharacterID = dubbingExt.CharacterID
		ttsInputData.CharacterAssetID = dubbingExt.CharacterAssetID
		ttsInputData.ScriptID = dubbingExt.ScriptID
	}
	logger.Debugf("CreateVcTask ttsInputData=%+v", util.JsonStr(ttsInputData))
	err = event.PushTTSLineInput(ttsInputData)
	if err != nil {
		logger.Errorf("PushTTSLineInput err=%+v", err)
		resp.Base = errcode.ErrorInternal.ToSvcBaseResp()
		return resp, nil
	}
	// push消息队列
	resp.Data = &vcxxjob.AsyncTTSRespData{
		TaskId: taskId,
	}
	return
}

func (s *DialogueVCService) removeNarration(content string) string {
	if len(content) > 0 {
		re := regexp.MustCompile(`(\(.*?\)|（.*?）)`)
		// 替换匹配字符为空字符串
		cleaned := re.ReplaceAllString(content, "")
		logger.Infof("re replaceAllString %s", cleaned)
		content = strings.TrimSpace(cleaned)
	}
	return content
}

func (s *DialogueVCService) UpdateVcTask(ctx context.Context, req *vcxxjob.UpdateVcTaskReq) (resp *vcxxjob.UpdateVcTaskResp, err error) {
	resp = &vcxxjob.UpdateVcTaskResp{Base: errcode.ErrOK.ToSvcBaseResp()}
	logger.Infof("UpdateVcTask req=%+v", util.JsonStr(req))
	vcTask, tmpErr := s.vcTaskInstance.GetVcTaskByTaskID(ctx, req.TaskId)
	if tmpErr != nil {
		logger.Errorf("GetVcTaskByTaskId err=%+v", tmpErr)
		resp.Base = errcode.ErrDBQueryFailed.ToSvcBaseResp()
		return resp, nil
	}
	if vcTask == nil {
		logger.Errorf("vcTask is nil")
		resp.Base = errcode.ErrDBQueryFailed.ToSvcBaseResp()
		return resp, nil
	}
	updates := map[string]interface{}{
		"tts_audio_url":      req.TtsAudioUrl,
		"tts_audio_duration": req.TtsAudioDuration,
	}
	taskStatus := vcxxjob.VcTaskStatus_STATUS_PROCESSING
	if vcTask.UseVc == 0 {
		switch req.Status {
		case "succeeded":
			taskStatus = vcxxjob.VcTaskStatus_STATUS_COMPLETED
		case "failed":
			taskStatus = vcxxjob.VcTaskStatus_STATUS_FAILED
		default:
			logger.Errorf("invalid status=%s", req.Status)
			resp.Base = errcode.ErrorParam.ToSvcBaseResp()
			return resp, nil
		}
		updates["status"] = taskStatus
	} else {
		if req.Status == "failed" {
			taskStatus = vcxxjob.VcTaskStatus_STATUS_FAILED
			updates["status"] = taskStatus
		}
	}
	tmpErr = s.UpdateVcTaskStatus(ctx, req.TaskId, updates)
	if tmpErr != nil {
		logger.Errorf("UpdateVcTaskByTaskId err=%+v", tmpErr)
		resp.Base = errcode.ErrDBUpdateFailed.ToSvcBaseResp()
		return resp, nil
	}
	if taskStatus == vcxxjob.VcTaskStatus_STATUS_FAILED {
		return resp, nil
	}
	// tts成功，根据是否需要变声， 回调通知或是继续进行变声
	lineId, _ := strconv.ParseInt(vcTask.OutId, 10, 64)
	if vcTask.UseVc > 0 {
		if lineId > 0 {
			var (
				dubbingExt *DubbingExt = nil
			)
			if len(vcTask.Ext) > 0 && vcTask.Source == 0 {
				if tmpErr := json.Unmarshal([]byte(vcTask.Ext), &dubbingExt); tmpErr != nil {
					logger.Errorf("json.Unmarshal err=%+v", tmpErr)
					resp.Base = errcode.ErrorParam.ToSvcBaseResp()
					return resp, nil
				}
				logger.Infof("dubbingExt=%+v", util.JsonStr(dubbingExt))
			}
			outputData := event.RvcInputData{
				LineID:           lineId,
				TaskId:           vcTask.TaskId,
				TtsAudioUrl:      req.TtsAudioUrl,
				TtsAudioDuration: int64(req.TtsAudioDuration),
			}
			if dubbingExt != nil {
				outputData.CharacterID = dubbingExt.CharacterID
				outputData.CharacterAssetID = dubbingExt.CharacterAssetID
				outputData.ScriptID = dubbingExt.ScriptID
			}
			if vcTask.UseRvc > 0 {
				outputData.UseRvc = true
			}
			logger.Debugf("UpdateVcTask PushVcLineInput outputData=%+v", util.JsonStr(outputData))
			tmpErr := event.PushVcLineInput(outputData)
			if tmpErr != nil {
				logger.Errorf("PushTTSLineOutput err=%+v outputData=%+v", tmpErr, outputData)
			}
		}
	} else {
		outputData := event.TTSLineOutputData{
			LineID:          lineId,
			DubbingDuration: req.GetTtsAudioDuration(),
			DubbingURL:      req.GetTtsAudioUrl(),
		}
		logger.Debugf("UpdateVcTask PushTTSLineOutput outputData=%+v", util.JsonStr(outputData))
		metric.ReportPv("dialogue_vc_succeed", 1, prometheus.Labels{})
		tmpErr := event.PushTTSLineOutput(outputData)
		if tmpErr != nil {
			logger.Errorf("PushTTSLineOutput err=%+v outputData=%+v", tmpErr, outputData)
		}
	}
	return
}

func (s *DialogueVCService) UpdateVcTaskStatus(ctx context.Context, taskId string, updates map[string]interface{}) (err error) {
	if len(taskId) == 0 || len(updates) == 0 {
		return
	}
	err = s.vcTaskInstance.UpdateVcTaskByTaskId(ctx, taskId, updates)
	return
}
