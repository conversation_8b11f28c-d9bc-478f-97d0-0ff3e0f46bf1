package config

import (
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/consul"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
)

type ScriptLinesConfig struct {
	// 默认台词列表
	DefaultLines [][]string `yaml:"default_lines" json:"default_lines"`
}

type ScriptConfig struct {
	// 热门评论点赞数量
	HotCommentLikeNum int32 `yaml:"hot_comment_like_num" json:"hot_comment_like_num"`

	// 审核开关配置
	ReviewSwitch ReviewSwitchConfig `yaml:"review_switch" json:"review_switch"`

	// 用户是否能看到自己未审核通过的内容（包括审核中、审核被拒绝等所有非已审核通过状态的内容）
	ShowOwnUnApprovedContent bool `yaml:"show_own_unapproved_content" json:"show_own_unapproved_content"`

	// 剧本质量分数阈值，用于过滤推荐和默认主题的剧本
	ScriptQualityScoreThreshold int32 `yaml:"script_quality_score_threshold" json:"script_quality_score_threshold"`
}

// ReviewItemConfig 单个审核项配置
type ReviewItemConfig struct {
	// 机审开关
	AutoReview bool `yaml:"auto_review" json:"auto_review"`
	// 人审开关
	ManualReview bool `yaml:"manual_review" json:"manual_review"`
}

// ReviewSwitchConfig 审核开关配置
type ReviewSwitchConfig struct {
	// 剧本审核开关（标题）
	Script ReviewItemConfig `yaml:"script" json:"script"`
	// 配音审核开关
	Dubbing ReviewItemConfig `yaml:"dubbing" json:"dubbing"`
	// 评论审核开关
	Comment ReviewItemConfig `yaml:"comment" json:"comment"`
}

// ReportReasonItem 举报原因配置项
type ReportReasonItem struct {
	ID     int32  `yaml:"id" json:"id"`         // 原因ID
	Reason string `yaml:"reason" json:"reason"` // 原因内容
	Sort   int32  `yaml:"sort" json:"sort"`     // 排序
}

// ReportReasonsConfig 举报原因配置
type ReportReasonsConfig struct {
	ReportReasons struct {
		Script  []ReportReasonItem `yaml:"script" json:"script"`   // 剧本举报原因
		Dubbing []ReportReasonItem `yaml:"dubbing" json:"dubbing"` // 配音举报原因
		Comment []ReportReasonItem `yaml:"comment" json:"comment"` // 评论举报原因
		User    []ReportReasonItem `yaml:"user" json:"user"`       // 用户举报原因
	} `yaml:"report_reasons" json:"report_reasons"`
}

var lineConf ScriptLinesConfig
var conf ScriptConfig
var reportConf ReportReasonsConfig

func InitConfig() {
	consul.WatchYaml("lines.yaml", &lineConf, func() {
		logger.Infof("svc script lines config=%+v", lineConf)
	})

	consul.WatchYaml("config.yaml", &conf, func() {
		logger.Infof("svc script config=%+v", conf)
	})

	consul.WatchYaml("report.yaml", &reportConf, func() {
		logger.Infof("svc script report config=%+v", reportConf)
	})
}

// 默认台词列表
func GetDefaultLines() [][]string {
	return lineConf.DefaultLines
}

// 热门评论点赞数量
func GetHotCommentLikeNum() int32 {
	if conf.HotCommentLikeNum <= 0 {
		return consts.CommentHotLikes
	}
	return conf.HotCommentLikeNum
}

// GetReportReasonsByType 根据举报类型获取举报原因列表
func GetReportReasonsByType(reportType svcscript.ReportType) []ReportReasonItem {
	switch reportType {
	case svcscript.ReportType_REPORT_TYPE_SCRIPT:
		return reportConf.ReportReasons.Script
	case svcscript.ReportType_REPORT_TYPE_DUBBING:
		return reportConf.ReportReasons.Dubbing
	case svcscript.ReportType_REPORT_TYPE_COMMENT:
		return reportConf.ReportReasons.Comment
	case svcscript.ReportType_RePORT_TYPE_USER:
		return reportConf.ReportReasons.User
	default:
		return nil
	}
}

// GetReportReasonByIDAndType 根据举报类型和原因ID获取举报原因
func GetReportReasonByIDAndType(reportType svcscript.ReportType, reasonID int32) *ReportReasonItem {
	reasons := GetReportReasonsByType(reportType)
	for _, reason := range reasons {
		if reason.ID == reasonID {
			return &reason
		}
	}
	return nil
}

// IsScriptAutoReviewEnabled 获取剧本机审开关状态
func IsScriptAutoReviewEnabled() bool {
	return conf.ReviewSwitch.Script.AutoReview
}

// IsScriptManualReviewEnabled 获取剧本人审开关状态
func IsScriptManualReviewEnabled() bool {
	return conf.ReviewSwitch.Script.ManualReview
}

// IsDubbingAutoReviewEnabled 获取配音机审开关状态
func IsDubbingAutoReviewEnabled() bool {
	return conf.ReviewSwitch.Dubbing.AutoReview
}

// IsDubbingManualReviewEnabled 获取配音人审开关状态
func IsDubbingManualReviewEnabled() bool {
	return conf.ReviewSwitch.Dubbing.ManualReview
}

// IsCommentAutoReviewEnabled 获取评论机审开关状态
func IsCommentAutoReviewEnabled() bool {
	return conf.ReviewSwitch.Comment.AutoReview
}

// IsCommentManualReviewEnabled 获取评论人审开关状态
func IsCommentManualReviewEnabled() bool {
	return conf.ReviewSwitch.Comment.ManualReview
}

// IsShowOwnUnApprovedContentEnabled 获取用户是否能看到自己未审核通过内容的开关状态
// 包括审核中、审核被拒绝等所有非已审核通过状态的内容
func IsShowOwnUnApprovedContentEnabled() bool {
	return conf.ShowOwnUnApprovedContent
}

// GetScriptQualityScoreThreshold 获取剧本质量分数阈值
// 用于过滤推荐和默认主题的剧本，只返回分数大于等于阈值的剧本
func GetScriptQualityScoreThreshold() int32 {
	if conf.ScriptQualityScoreThreshold <= 0 {
		return 30 // 默认值为30
	}
	return conf.ScriptQualityScoreThreshold
}
