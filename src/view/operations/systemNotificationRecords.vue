<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule">
        <el-form-item label="标题" prop="title">
          <el-input v-model="searchInfo.title" placeholder="搜索标题" :clearable="true" @keyup.enter="onSubmit" />
        </el-form-item>
        <el-form-item label="接收者类型" prop="receiver_type">
          <el-select v-model="searchInfo.receiver_type" placeholder="请选择接收者类型" :clearable="true">
            <el-option label="全部用户" :value="1" />
            <el-option label="指定用户" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="发送状态" prop="send_status">
          <el-select v-model="searchInfo.send_status" placeholder="请选择发送状态" :clearable="true">
            <el-option label="发送中" :value="1" />
            <el-option label="发送成功" :value="2" />
            <el-option label="发送失败" :value="3" />
            <el-option label="部分失败" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作员" prop="operator_name">
          <el-input v-model="searchInfo.operator_name" placeholder="搜索操作员" :clearable="true" @keyup.enter="onSubmit" />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 380px;"
            @change="handleTimeRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">发送系统通知</el-button>
        <el-button type="primary" icon="refresh" @click="getTableData">刷新</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%; min-width: 1280px;"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @sort-change="sortChange"
      >
        <el-table-column align="left" label="时间" prop="CreatedAt" sortable="custom" width="170">
          <template #default="scope">{{ formatBoolean(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="left" label="标题" prop="title" sortable="custom" min-width="160" show-overflow-tooltip />
        <el-table-column align="left" label="内容" prop="content" min-width="220" show-overflow-tooltip />
        <el-table-column align="left" label="类型" prop="receiver_type_str" sortable="custom" width="80" />
        <el-table-column align="left" label="数量" prop="receiver_count" sortable="custom" width="80" />
        <el-table-column align="left" label="状态" prop="send_status" sortable="custom" width="90">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.send_status)" size="small">
              {{ scope.row.send_status_str }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="成功" prop="success_count" sortable="custom" width="65" />
        <el-table-column align="left" label="失败" prop="failure_count" sortable="custom" width="65" />
        <el-table-column align="left" label="耗时" prop="send_duration_str" sortable="custom" width="80" />
        <el-table-column align="left" label="操作员" prop="operator_name" sortable="custom" width="120" show-overflow-tooltip />
        <el-table-column align="left" label="操作" fixed="right" width="140">
          <template #default="scope">
            <div class="operation-buttons-inline">
              <el-button type="primary" link size="small" @click="viewDetail(scope.row)">
                <el-icon><InfoFilled /></el-icon>
                详情
              </el-button>
              <el-button v-if="scope.row.error_message" type="danger" link size="small" @click="viewError(scope.row)">
                <el-icon><Warning /></el-icon>
                错误
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailVisible" title="通知详情" width="60%" :before-close="closeDetail">
      <div v-if="currentRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="标题">{{ currentRecord.title }}</el-descriptions-item>
          <el-descriptions-item label="内容">{{ currentRecord.content }}</el-descriptions-item>
          <el-descriptions-item label="接收者类型">{{ currentRecord.receiver_type_str }}</el-descriptions-item>
          <el-descriptions-item label="接收者数量">{{ currentRecord.receiver_count }}</el-descriptions-item>
          <el-descriptions-item label="发送状态">
            <el-tag :type="getStatusTagType(currentRecord.send_status)">
              {{ currentRecord.send_status_str }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="成功数量">{{ currentRecord.success_count }}</el-descriptions-item>
          <el-descriptions-item label="失败数量">{{ currentRecord.failure_count }}</el-descriptions-item>
          <el-descriptions-item label="发送耗时">{{ currentRecord.send_duration_str }}</el-descriptions-item>
          <el-descriptions-item label="操作员">{{ currentRecord.operator_name }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatBoolean(currentRecord.CreatedAt) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatBoolean(currentRecord.send_start_time) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ currentRecord.send_end_time ? formatBoolean(currentRecord.send_end_time) : '-' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 系统消息数据 -->
        <div style="margin-top: 20px;">
          <el-card>
            <template #header>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="font-weight: 600;">📋 系统消息数据</span>
                <el-button type="primary" size="small" @click="copySystemMsgData">
                  <el-icon style="margin-right: 4px;"><DocumentCopy /></el-icon>
                  复制
                </el-button>
              </div>
            </template>
            <el-input
              v-model="formattedSystemMsgData"
              type="textarea"
              :rows="12"
              readonly
              style="font-family: 'Courier New', monospace; font-size: 12px;"
              placeholder="系统消息数据"
            />
          </el-card>
        </div>

        <div v-if="currentRecord.error_message" style="margin-top: 20px;">
          <el-alert title="错误信息" type="error" :closable="false">
            <pre>{{ currentRecord.error_message }}</pre>
          </el-alert>
        </div>
      </div>
    </el-dialog>

    <!-- 错误信息弹窗 -->
    <el-dialog v-model="errorVisible" title="错误信息" width="50%" :before-close="closeError">
      <el-alert title="发送失败原因" type="error" :closable="false">
        <pre>{{ currentError }}</pre>
      </el-alert>
    </el-dialog>

    <!-- 发送系统通知弹窗 -->
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="发送系统通知" width="80%">
      <el-form ref="elFormRef" :model="formData" :rules="rule" label-width="120px">
        <!-- 接收者配置 -->
        <el-form-item label="接收者:" prop="to_uid">
          <el-radio-group v-model="receiverType" @change="handleReceiverTypeChange">
            <el-radio :label="'all'">全部用户</el-radio>
            <el-radio :label="'specific'">指定用户</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 指定用户ID输入区域 -->
        <el-form-item v-if="receiverType === 'specific'" label="用户ID:" class="user-id-form-item">
          <div class="user-id-input-section">
            <div class="user-id-input-container">
              <el-input
                v-model="userIdInput"
                :clearable="true"
                placeholder="请输入用户ID，支持逗号分隔批量添加，按回车确认"
                @keyup.enter="addUserId"
                style="flex: 1;"
              />
              <el-button
                type="primary"
                @click="addUserId"
                :disabled="!userIdInput.trim()"
                style="margin-left: 8px;"
              >
                添加
              </el-button>
            </div>

            <!-- 已选用户ID标签 -->
            <div v-if="selectedUserIds.length > 0" class="selected-users-container">
              <div class="selected-users-header">
                <div class="selected-users-label">已选用户ID ({{ selectedUserIds.length }})：</div>
                <el-button
                  type="danger"
                  size="small"
                  link
                  @click="clearAllUserIds"
                  style="padding: 0; font-size: 12px;"
                >
                  清空全部
                </el-button>
              </div>
              <div class="user-tags-container">
                <el-tag
                  v-for="(userId, index) in selectedUserIds"
                  :key="index"
                  closable
                  @close="removeUserId(index)"
                  class="user-id-tag"
                  type="info"
                >
                  {{ userId }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 标题配置 -->
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>📝 标题配置</span>
            </div>
          </template>

          <el-form-item label="标题模板:" prop="system_msg.title.tpl">
            <el-input
              v-model="formData.system_msg.title.tpl"
              :clearable="true"
              placeholder="请输入标题模板，支持变量如{{nickname}}"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="标题颜色:">
                <el-color-picker v-model="formData.system_msg.title.tpl_color" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="标题跳转类型:">
                <el-select v-model="formData.system_msg.title.type" placeholder="请选择跳转类型">
                  <el-option
                    v-for="(value, key) in jumpTypes"
                    :key="key"
                    :label="getJumpTypeLabel(key)"
                    :value="value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="标题跳转地址:" v-if="formData.system_msg.title.type > 0">
                <el-autocomplete
                  v-model="formData.system_msg.title.url"
                  :fetch-suggestions="queryJumpUrlSuggestions"
                  placeholder="请选择或输入跳转地址"
                  style="width: 100%"
                  clearable
                  :trigger-on-focus="false"
                  @select="handleJumpUrlSelect"
                >
                  <template #default="{ item }">
                    <div class="jump-url-suggestion">
                      <div class="suggestion-label">{{ item.label }}</div>
                      <div class="suggestion-value">{{ item.value }}</div>
                      <div v-if="item.params" class="suggestion-params">{{ item.params }}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <!-- 参数说明显示 -->
                <div
                  v-if="formData.system_msg.title.url && isPredefinedUrl(formData.system_msg.title.url) && getUrlParams(formData.system_msg.title.url)"
                  class="url-params-hint"
                >
                  <el-icon class="params-icon"><InfoFilled /></el-icon>
                  <span class="params-text">参数说明：{{ getUrlParams(formData.system_msg.title.url) }}</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 标题中的变量配置 -->
          <div v-if="titleVariables.length > 0" style="margin-top: 20px;">
            <el-divider content-position="left">
              <span style="color: #409EFF; font-weight: 600;">🔧 标题变量配置</span>
            </el-divider>

            <div v-for="variable in titleVariables" :key="variable" class="variable-config-inline">
              <div class="variable-section-inline">
                <div class="section-label-inline">变量 {{variable}} 配置:</div>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="文本内容:">
                      <el-input
                        v-model="formData.system_msg.title.data[getVariableName(variable)].text"
                        placeholder="请输入文本内容"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="文字颜色:">
                      <el-color-picker v-model="formData.system_msg.title.data[getVariableName(variable)].color" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="字体大小:">
                      <el-input-number
                        v-model="formData.system_msg.title.data[getVariableName(variable)].size"
                        :min="10"
                        :max="30"
                        controls-position="right"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="跳转类型:">
                      <el-select v-model="formData.system_msg.title.data[getVariableName(variable)].type" placeholder="请选择跳转类型">
                        <el-option
                          v-for="(value, key) in jumpTypes"
                          :key="key"
                          :label="getJumpTypeLabel(key)"
                          :value="value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="跳转地址:" v-if="formData.system_msg.title.data[getVariableName(variable)].type > 0">
                      <el-autocomplete
                        v-model="formData.system_msg.title.data[getVariableName(variable)].url"
                        :fetch-suggestions="queryJumpUrlSuggestions"
                        placeholder="请选择或输入跳转地址"
                        style="width: 100%"
                        clearable
                        :trigger-on-focus="false"
                        @select="handleJumpUrlSelect"
                      >
                        <template #default="{ item }">
                          <div class="jump-url-suggestion">
                            <div class="suggestion-label">{{ item.label }}</div>
                            <div class="suggestion-value">{{ item.value }}</div>
                            <div v-if="item.params" class="suggestion-params">{{ item.params }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      <!-- 参数说明显示 -->
                      <div
                        v-if="formData.system_msg.title.data[getVariableName(variable)].url && isPredefinedUrl(formData.system_msg.title.data[getVariableName(variable)].url) && getUrlParams(formData.system_msg.title.data[getVariableName(variable)].url)"
                        class="url-params-hint"
                      >
                        <el-icon class="params-icon"><InfoFilled /></el-icon>
                        <span class="params-text">参数说明：{{ getUrlParams(formData.system_msg.title.data[getVariableName(variable)].url) }}</span>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 内容配置 -->
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>📄 内容配置</span>
            </div>
          </template>

          <el-form-item label="内容模板:" prop="system_msg.content.tpl">
            <el-input
              v-model="formData.system_msg.content.tpl"
              :clearable="true"
              placeholder="请输入内容模板，支持变量如{{score}}"
              type="textarea"
              :rows="3"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="内容颜色:">
                <el-color-picker v-model="formData.system_msg.content.tpl_color" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="内容跳转类型:">
                <el-select v-model="formData.system_msg.content.type" placeholder="请选择跳转类型">
                  <el-option
                    v-for="(value, key) in jumpTypes"
                    :key="key"
                    :label="getJumpTypeLabel(key)"
                    :value="value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="内容跳转地址:" v-if="formData.system_msg.content.type > 0">
                <el-autocomplete
                  v-model="formData.system_msg.content.url"
                  :fetch-suggestions="queryJumpUrlSuggestions"
                  placeholder="请选择或输入跳转地址"
                  style="width: 100%"
                  clearable
                  :trigger-on-focus="false"
                  @select="handleJumpUrlSelect"
                >
                  <template #default="{ item }">
                    <div class="jump-url-suggestion">
                      <div class="suggestion-label">{{ item.label }}</div>
                      <div class="suggestion-value">{{ item.value }}</div>
                      <div v-if="item.params" class="suggestion-params">{{ item.params }}</div>
                    </div>
                  </template>
                </el-autocomplete>
                <!-- 参数说明显示 -->
                <div
                  v-if="formData.system_msg.content.url && isPredefinedUrl(formData.system_msg.content.url) && getUrlParams(formData.system_msg.content.url)"
                  class="url-params-hint"
                >
                  <el-icon class="params-icon"><InfoFilled /></el-icon>
                  <span class="params-text">参数说明：{{ getUrlParams(formData.system_msg.content.url) }}</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 内容中的变量配置 -->
          <div v-if="contentVariables.length > 0" style="margin-top: 20px;">
            <el-divider content-position="left">
              <span style="color: #409EFF; font-weight: 600;">🔧 内容变量配置</span>
            </el-divider>

            <div v-for="variable in contentVariables" :key="variable" class="variable-config-inline">
              <div class="variable-section-inline">
                <div class="section-label-inline">变量 {{variable}} 配置:</div>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="文本内容:">
                      <el-input
                        v-model="formData.system_msg.content.data[getVariableName(variable)].text"
                        placeholder="请输入文本内容"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="文字颜色:">
                      <el-color-picker v-model="formData.system_msg.content.data[getVariableName(variable)].color" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="字体大小:">
                      <el-input-number
                        v-model="formData.system_msg.content.data[getVariableName(variable)].size"
                        :min="10"
                        :max="30"
                        controls-position="right"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="跳转类型:">
                      <el-select v-model="formData.system_msg.content.data[getVariableName(variable)].type" placeholder="请选择跳转类型">
                        <el-option
                          v-for="(value, key) in jumpTypes"
                          :key="key"
                          :label="getJumpTypeLabel(key)"
                          :value="value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="跳转地址:" v-if="formData.system_msg.content.data[getVariableName(variable)].type > 0">
                      <el-autocomplete
                        v-model="formData.system_msg.content.data[getVariableName(variable)].url"
                        :fetch-suggestions="queryJumpUrlSuggestions"
                        placeholder="请选择或输入跳转地址"
                        style="width: 100%"
                        clearable
                        :trigger-on-focus="false"
                        @select="handleJumpUrlSelect"
                      >
                        <template #default="{ item }">
                          <div class="jump-url-suggestion">
                            <div class="suggestion-label">{{ item.label }}</div>
                            <div class="suggestion-value">{{ item.value }}</div>
                            <div v-if="item.params" class="suggestion-params">{{ item.params }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      <!-- 参数说明显示 -->
                      <div
                        v-if="formData.system_msg.content.data[getVariableName(variable)].url && isPredefinedUrl(formData.system_msg.content.data[getVariableName(variable)].url) && getUrlParams(formData.system_msg.content.data[getVariableName(variable)].url)"
                        class="url-params-hint"
                      >
                        <el-icon class="params-icon"><InfoFilled /></el-icon>
                        <span class="params-text">参数说明：{{ getUrlParams(formData.system_msg.content.data[getVariableName(variable)].url) }}</span>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 附加配置 -->
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>🎨 附加配置</span>
            </div>
          </template>

          <el-form-item label="背景图片:">
            <SelectImage v-model="formData.system_msg.bgm_url" />
          </el-form-item>

          <el-form-item label="整体跳转地址:">
            <el-autocomplete
              v-model="formData.system_msg.jump_url"
              :fetch-suggestions="queryJumpUrlSuggestions"
              placeholder="请选择或输入整体跳转地址（可选）"
              style="width: 100%"
              clearable
              :trigger-on-focus="false"
              @select="handleJumpUrlSelect"
            >
              <template #default="{ item }">
                <div class="jump-url-suggestion">
                  <div class="suggestion-label">{{ item.label }}</div>
                  <div class="suggestion-value">{{ item.value }}</div>
                  <div v-if="item.params" class="suggestion-params">{{ item.params }}</div>
                </div>
              </template>
            </el-autocomplete>
            <!-- 参数说明显示 -->
            <div
              v-if="formData.system_msg.jump_url && isPredefinedUrl(formData.system_msg.jump_url) && getUrlParams(formData.system_msg.jump_url)"
              class="url-params-hint"
            >
              <el-icon class="params-icon"><InfoFilled /></el-icon>
              <span class="params-text">参数说明：{{ getUrlParams(formData.system_msg.jump_url) }}</span>
            </div>
          </el-form-item>
        </el-card>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="sendNotification" :loading="btnLoading">
            {{ btnLoading ? '发送中...' : '发送通知' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, Warning, DocumentCopy } from '@element-plus/icons-vue'
import { getSystemNotificationRecords, sendSystemNotification, getTemplateJumpTypes, getJumpUrlOptions } from '@/api/operations/systemNotification'
import { formatTimeToStr } from '@/utils/date'
import SelectImage from '@/components/selectImage/selectImage.vue'

defineOptions({
  name: 'SystemNotificationManagement'
})

// 响应式数据
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
const searchTimeRange = ref([])
const detailVisible = ref(false)
const errorVisible = ref(false)
const currentRecord = ref(null)
const currentError = ref('')

// 发送通知相关数据
const dialogFormVisible = ref(false)
const btnLoading = ref(false)
const receiverType = ref('all')
const jumpTypes = ref({})
const jumpUrlOptions = ref([])
const elFormRef = ref()
const userIdInput = ref('')
const selectedUserIds = ref([])

// 表单数据
const formData = ref({
  to_uid: -1,
  to_uids: [],
  content_type: 150,
  system_msg: {
    title: {
      tpl: '',
      data: {},
      image_url: '',
      user_id: 0,
      tpl_color: '#000000',
      type: 0,
      url: ''
    },
    bgm_url: '',
    jump_url: '',
    content: {
      tpl: '',
      data: {},
      image_url: '',
      user_id: 0,
      tpl_color: '#000000',
      type: 0,
      url: ''
    },
    time: 0
  }
})

// 表单验证规则
const rule = reactive({
  to_uid: [
    {
      required: true,
      validator: (_, value, callback) => {
        if (receiverType.value === 'all') {
          if (value !== -1) {
            callback(new Error('全部用户时应为-1'))
          } else {
            callback()
          }
        } else {
          // 指定用户时，检查是否有选中的用户ID
          if (selectedUserIds.value.length === 0) {
            callback(new Error('请至少添加一个用户ID'))
          } else {
            callback()
          }
        }
      },
      trigger: 'blur'
    }
  ],
  content_type: [{ required: true, message: '请选择内容类型', trigger: 'change' }],
  'system_msg.title.tpl': [{ required: true, message: '请输入标题模板', trigger: 'blur' }],
  'system_msg.content.tpl': [{ required: true, message: '请输入内容模板', trigger: 'blur' }]
})

const searchRule = reactive({
  createdAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && Date.parse(searchInfo.value.startCreatedAt) - Date.parse(searchInfo.value.endCreatedAt) > 0) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ]
})

// 计算属性：从模板中提取变量
const templateVariables = computed(() => {
  const titleVars = extractVariables(formData.value.system_msg.title.tpl)
  const contentVars = extractVariables(formData.value.system_msg.content.tpl)
  return [...new Set([...titleVars, ...contentVars])]
})

const titleVariables = computed(() => {
  return extractVariables(formData.value.system_msg.title.tpl)
})

const contentVariables = computed(() => {
  return extractVariables(formData.value.system_msg.content.tpl)
})

// 格式化系统消息数据
const formattedSystemMsgData = computed(() => {
  if (!currentRecord.value) {
    return '暂无记录数据'
  }

  if (!currentRecord.value.system_msg_data) {
    return '暂无系统消息数据'
  }

  try {
    const data = JSON.parse(currentRecord.value.system_msg_data)
    return JSON.stringify(data, null, 2)
  } catch (error) {
    return `原始数据：\n${currentRecord.value.system_msg_data}`
  }
})

// 获取变量的纯名称（去掉{{}}）
function getVariableName(variable) {
  return variable.replace(/\{\{|\}\}/g, '')
}

// 提取模板变量的函数
function extractVariables(template) {
  if (!template) return []
  const matches = template.match(/\{\{(\w+)\}\}/g)
  if (!matches) return []
  // 保留完整的{{变量名}}格式
  return matches
}

// 监听模板变化，自动初始化变量数据
watch(templateVariables, (newVars) => {
  newVars.forEach(variable => {
    const varName = getVariableName(variable) // 获取纯变量名作为key

    // 初始化标题数据
    if (titleVariables.value.includes(variable) && !formData.value.system_msg.title.data[varName]) {
      formData.value.system_msg.title.data[varName] = {
        text: '',
        color: '#000000',
        url: '',
        type: 0,
        name: variable, // 保持完整的{{变量名}}格式
        ext: '',
        underline: false,
        size: 14
      }
    }

    // 初始化内容数据
    if (contentVariables.value.includes(variable) && !formData.value.system_msg.content.data[varName]) {
      formData.value.system_msg.content.data[varName] = {
        text: '',
        color: '#000000',
        url: '',
        type: 0,
        name: variable, // 保持完整的{{变量名}}格式
        ext: '',
        underline: false,
        size: 14
      }
    }
  })
}, { deep: true })

// 获取表格数据
const getTableData = async() => {
  const table = await getSystemNotificationRecords({
    page: page.value,
    pageSize: pageSize.value,
    ...searchInfo.value
  })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

// 初始化
getTableData()

// 获取跳转类型
const getJumpTypes = async() => {
  const res = await getTemplateJumpTypes()
  if (res.code === 0) {
    jumpTypes.value = res.data.jumpTypes
  }
}

getJumpTypes()

// 获取跳转地址选项
const fetchJumpUrlOptions = async() => {
  const res = await getJumpUrlOptions()
  if (res.code === 0) {
    jumpUrlOptions.value = res.data
  }
}

fetchJumpUrlOptions()

// 获取跳转地址的参数说明
const getUrlParams = (url) => {
  if (!url) return ''
  const option = jumpUrlOptions.value.find(opt => opt.value === url)
  return option ? option.params : ''
}

// 检查是否为预定义地址
const isPredefinedUrl = (url) => {
  if (!url) return false
  return jumpUrlOptions.value.some(opt => opt.value === url)
}

// 查询跳转地址建议
const queryJumpUrlSuggestions = (queryString, callback) => {
  const suggestions = jumpUrlOptions.value.filter(option => {
    if (!queryString) return true
    const query = queryString.toLowerCase()
    return option.label.toLowerCase().includes(query) ||
           option.value.toLowerCase().includes(query)
  })
  callback(suggestions)
}

// 处理跳转地址选择
const handleJumpUrlSelect = (item) => {
  console.log('Selected jump url:', item)
  // 选择后不需要特殊处理，el-autocomplete会自动设置值
}

// 搜索
const onSubmit = () => {
  page.value = 1
  pageSize.value = 10
  getTableData()
}

// 重置
const onReset = () => {
  searchInfo.value = {}
  searchTimeRange.value = []
  getTableData()
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 排序
const sortChange = ({ prop, order }) => {
  const orderMap = {
    ascending: false,
    descending: true
  }
  if (prop && order) {
    searchInfo.value.order_key = prop
    searchInfo.value.desc = orderMap[order]
  } else {
    delete searchInfo.value.order_key
    delete searchInfo.value.desc
  }
  getTableData()
}

// 时间范围处理
const handleTimeRangeChange = (val) => {
  if (val && val.length === 2) {
    searchInfo.value.start_time = val[0]
    searchInfo.value.end_time = val[1]
  } else {
    delete searchInfo.value.start_time
    delete searchInfo.value.end_time
  }
}

// 查看详情
const viewDetail = (row) => {
  currentRecord.value = row
  detailVisible.value = true
}

const closeDetail = () => {
  detailVisible.value = false
  currentRecord.value = null
}

// 查看错误
const viewError = (row) => {
  currentError.value = row.error_message
  errorVisible.value = true
}

const closeError = () => {
  errorVisible.value = false
  currentError.value = ''
}

// 复制系统消息数据
const copySystemMsgData = async () => {
  try {
    await navigator.clipboard.writeText(formattedSystemMsgData.value)
    ElMessage.success('系统消息数据已复制到剪贴板')
  } catch (error) {
    // 如果clipboard API不可用，使用传统方法
    const textArea = document.createElement('textarea')
    textArea.value = formattedSystemMsgData.value
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('系统消息数据已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    1: '', // 发送中
    2: 'success', // 发送成功
    3: 'danger', // 发送失败
    4: 'warning' // 部分失败
  }
  return statusMap[status] || ''
}

// 格式化时间
const formatBoolean = (time) => {
  if (new Date(time).getTime() > 0) {
    return formatTimeToStr(time)
  } else {
    return ''
  }
}

// 发送通知相关方法
const openDialog = () => {
  resetForm()
  dialogFormVisible.value = true
}

const closeDialog = () => {
  dialogFormVisible.value = false
  resetForm()
}

// 重置表单
function resetForm() {
  formData.value = {
    to_uid: -1,
    to_uids: [],
    content_type: 150,
    system_msg: {
      title: {
        tpl: '',
        data: {},
        image_url: '',
        user_id: 0,
        tpl_color: '#000000',
        type: 0,
        url: ''
      },
      bgm_url: '',
      jump_url: '',
      content: {
        tpl: '',
        data: {},
        image_url: '',
        user_id: 0,
        tpl_color: '#000000',
        type: 0,
        url: ''
      },
      time: 0
    }
  }
  receiverType.value = 'all'
  selectedUserIds.value = []
  userIdInput.value = ''
}

// 处理接收者类型变化
function handleReceiverTypeChange(type) {
  if (type === 'all') {
    formData.value.to_uid = -1
    formData.value.to_uids = []
    selectedUserIds.value = []
    userIdInput.value = ''
  } else {
    formData.value.to_uid = null
    formData.value.to_uids = []
    selectedUserIds.value = []
  }

  // 清除to_uid字段的验证错误
  if (elFormRef.value) {
    elFormRef.value.clearValidate('to_uid')
  }
}

// 添加用户ID
function addUserId() {
  const input = userIdInput.value.trim()
  if (!input) return

  // 支持多种分隔符：逗号、分号、空格、换行
  const userIds = input.split(/[,;，；\s\n]+/).filter(id => id.trim())

  let addedCount = 0
  let duplicateCount = 0
  let invalidCount = 0

  userIds.forEach(userId => {
    const trimmedId = userId.trim()
    if (trimmedId && !isNaN(trimmedId) && parseInt(trimmedId) > 0) {
      const userIdNum = parseInt(trimmedId)
      if (!selectedUserIds.value.includes(userIdNum)) {
        selectedUserIds.value.push(userIdNum)
        addedCount++
      } else {
        duplicateCount++
      }
    } else if (trimmedId) {
      invalidCount++
    }
  })

  // 清空输入框
  userIdInput.value = ''

  // 显示添加结果
  if (addedCount > 0) {
    ElMessage.success(`成功添加 ${addedCount} 个用户ID`)
    // 清除验证错误
    if (elFormRef.value) {
      elFormRef.value.clearValidate('to_uid')
    }
  }

  if (duplicateCount > 0) {
    ElMessage.warning(`${duplicateCount} 个用户ID已存在，已跳过`)
  }

  if (invalidCount > 0) {
    ElMessage.warning(`${invalidCount} 个无效的用户ID，已跳过`)
  }
}

// 删除用户ID
function removeUserId(index) {
  selectedUserIds.value.splice(index, 1)
}

// 清空所有用户ID
function clearAllUserIds() {
  selectedUserIds.value = []
  ElMessage.success('已清空所有用户ID')
}

// 获取跳转类型标签
function getJumpTypeLabel(key) {
  const labels = {
    jump_type_init: '无跳转',
    jump_web: '网页跳转',
    jump_page: '页面跳转',
    jump_update_app: '更新应用',
    jump_session_top: '会话置顶',
    jump_script: '剧本跳转',
    jump_user_info: '用户信息'
  }
  return labels[key] || key
}

// 发送通知
async function sendNotification() {
  if (!elFormRef.value) return

  try {
    await elFormRef.value.validate()
  } catch (error) {
    return
  }

  btnLoading.value = true

  try {
    // 准备提交数据
    const submitData = { ...formData.value }

    // 根据接收者类型设置正确的字段
    if (receiverType.value === 'all') {
      submitData.to_uid = -1
      submitData.to_uids = []
    } else {
      submitData.to_uid = 0 // 指定用户时设为0
      submitData.to_uids = [...selectedUserIds.value]
    }

    // 打印提交的数据，方便调试
    console.log('提交的表单数据:', JSON.stringify(submitData, null, 2))

    const res = await sendSystemNotification(submitData)
    if (res.code === 0) {
      ElMessage.success('系统通知发送成功')
      closeDialog()
      // 刷新列表
      getTableData()
    } else {
      ElMessage.error(res.msg || '发送失败')
    }
  } catch (error) {
    ElMessage.error('发送失败')
    console.error('发送系统通知失败:', error)
  } finally {
    btnLoading.value = false
  }
}
</script>

<style scoped>
.table-button {
  margin-right: 10px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: inherit;
  margin: 0;
}

/* 卡片样式 */
.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

/* 用户ID表单项样式 */
.user-id-form-item {
  margin-bottom: 20px;
}

/* 用户ID输入区域 */
.user-id-input-section {
  width: 100%;
}

/* 用户ID输入容器样式 */
.user-id-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

/* 已选用户容器样式 */
.selected-users-container {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  min-height: 60px;
  transition: all 0.3s ease;
}

/* 已选用户头部 */
.selected-users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.selected-users-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  margin: 0;
}

/* 用户标签容器 */
.user-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

/* 用户ID标签样式 */
.user-id-tag {
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 4px;
  white-space: nowrap;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 标签悬停效果 */
.user-id-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* 内联变量配置样式 */
.variable-config-inline {
  margin-bottom: 20px;
}

.variable-section-inline {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 15px;
}

.section-label-inline {
  font-weight: 500;
  color: #495057;
  margin-bottom: 15px;
  font-size: 14px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

/* 确保弹窗内容不会超出屏幕 */
:deep(.el-dialog__wrapper) {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 5vh 0;
}

/* 操作按钮布局优化 */
:deep(.el-table .el-table__fixed-right) {
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-table__fixed-right-patch) {
  background-color: #fff;
}

/* 操作按钮内联容器 - 确保按钮不换行 */
.operation-buttons-inline {
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
  min-width: max-content;
}

/* 操作列按钮样式 */
.operation-buttons-inline .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  flex-shrink: 0;
}

.operation-buttons-inline .el-button .el-icon {
  margin-right: 3px;
  font-size: 12px;
}

/* 表格优化 */
:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table th) {
  padding: 8px 0;
  font-size: 13px;
  font-weight: 600;
  white-space: nowrap;
}

:deep(.el-table td) {
  padding: 8px 0;
}

/* 表头排序按钮优化 */
:deep(.el-table th .cell) {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  white-space: nowrap;
  overflow: hidden;
}

:deep(.el-table th .caret-wrapper) {
  margin-left: 4px;
  flex-shrink: 0;
}

/* 防止表头文字和排序按钮换行 */
:deep(.el-table__header-wrapper) {
  overflow-x: auto;
}

:deep(.el-table__header) {
  width: 100%;
  min-width: max-content;
}

/* 状态标签优化 */
:deep(.el-tag--small) {
  height: 20px;
  line-height: 18px;
  font-size: 11px;
  padding: 0 6px;
}

/* 搜索表单优化 */
:deep(.gva-search-box .el-form--inline .el-form-item) {
  margin-right: 16px;
  margin-bottom: 12px;
}

:deep(.gva-search-box .el-date-editor) {
  width: 380px !important;
}

/* 表格响应式优化 */
@media (max-width: 1600px) {
  :deep(.gva-search-box .el-date-editor) {
    width: 320px !important;
  }
}

@media (max-width: 1400px) {
  .operation-buttons-inline {
    gap: 4px;
  }

  .operation-buttons-inline .el-button {
    padding: 3px 6px;
    font-size: 11px;
  }

  .operation-buttons-inline .el-button .el-icon {
    margin-right: 2px;
    font-size: 11px;
  }

  :deep(.gva-search-box .el-date-editor) {
    width: 280px !important;
  }
}

@media (max-width: 1200px) {
  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table th),
  :deep(.el-table td) {
    padding: 6px 0;
  }

  :deep(.gva-search-box .el-date-editor) {
    width: 240px !important;
  }
}

/* 表格滚动条优化 */
:deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 跳转地址建议样式 */
.jump-url-suggestion {
  padding: 8px 0;
  line-height: 1.4;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.suggestion-label {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  margin-bottom: 4px;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.suggestion-value {
  color: #8492a6;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  margin-bottom: 3px;
  line-height: 1.4;
  word-break: break-all;
  white-space: normal;
  max-width: 100%;
}

.suggestion-params {
  color: #909399;
  font-size: 11px;
  font-style: italic;
  line-height: 1.3;
  word-break: break-word;
  white-space: normal;
}

/* autocomplete 下拉建议样式 */
:deep(.el-autocomplete-suggestion) {
  .el-autocomplete-suggestion__list {
    max-height: 300px;
    overflow-y: auto;
  }

  .el-autocomplete-suggestion__list li {
    height: auto !important;
    min-height: 60px !important;
    padding: 8px 20px !important;
    line-height: 1.4 !important;
    white-space: normal !important;
    overflow: visible !important;
  }

  .el-autocomplete-suggestion__list li:hover {
    background-color: #f5f7fa !important;
  }
}

/* 参数说明提示样式 */
.url-params-hint {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  font-size: 12px;
  line-height: 1.4;
}

.params-icon {
  color: #409eff;
  font-size: 14px;
  margin-top: 1px;
  flex-shrink: 0;
}

.params-text {
  color: #606266;
  word-break: break-word;
  flex: 1;
}
</style>
