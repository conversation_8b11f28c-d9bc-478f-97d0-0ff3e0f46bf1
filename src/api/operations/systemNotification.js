import service from '@/utils/request'

// @Tags SystemNotification
// @Summary 发送系统通知
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body object true "发送系统通知"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"发送成功"}"
// @Router /systemNotification/sendSystemNotification [post]
export const sendSystemNotification = (data) => {
  return service({
    url: '/systemNotification/sendSystemNotification',
    method: 'post',
    data
  })
}

// @Tags SystemNotification
// @Summary 获取跳转类型枚举
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /systemNotification/getTemplateJumpTypes [get]
export const getTemplateJumpTypes = () => {
  return service({
    url: '/systemNotification/getTemplateJumpTypes',
    method: 'get'
  })
}

// 获取系统通知记录列表
export const getSystemNotificationRecords = (params) => {
  return service({
    url: '/systemNotification/getSystemNotificationRecords',
    method: 'get',
    params
  })
}

// @Tags SystemNotification
// @Summary 获取跳转地址选项列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":[],"msg":"获取成功"}"
// @Router /systemNotification/getJumpUrlOptions [get]
export const getJumpUrlOptions = () => {
  return service({
    url: '/systemNotification/getJumpUrlOptions',
    method: 'get'
  })
}
