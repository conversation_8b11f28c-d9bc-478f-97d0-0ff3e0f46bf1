package services

import (
	"context"
	"errors"
	"fmt"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/svcaccount/config"
	"regexp"
	"strconv"
	"strings"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/basemsgtransfer"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcchat"

	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/event"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"

	"crypto/sha256"

	"github.com/golang-jwt/jwt/v4"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor/bizcontext"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
	"new-gitlab.xunlei.cn/vcproject/backends/svcaccount/cache"
	"new-gitlab.xunlei.cn/vcproject/backends/svcaccount/model"
)

// TokenConfig JWT Token配置
type TokenConfig struct {
	jwtSecret       string
	AccessTokenTTL  time.Duration // 访问令牌过期时间
	RefreshTokenTTL time.Duration // 刷新令牌过期时间
	ClockSkew       time.Duration // 允许的时钟偏差
	MaxRefreshTime  time.Duration // 刷新令牌最大刷新时间
}

// AuthTokenInfo 认证令牌信息
type AuthTokenInfo struct {
	UserId       int64  `json:"user_id"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

// JWTClaims JWT载荷信息
type JWTClaims struct {
	UserId      int64            `json:"user_id"`     // 用户ID
	DeviceId    string           `json:"device_id"`   // 设备ID
	TokenType   consts.TokenType `json:"token_type"`  // Token类型
	Platform    int32            `json:"platform"`    // 平台类型
	Fingerprint string           `json:"fingerprint"` // 设备指纹
	jwt.RegisteredClaims
}

type AccountService struct {
	userModel      model.UserModelInterface
	userAuthModel  model.UserAuthModelInterface
	regInfoModel   model.UserRegisterInfoModelInterface
	userAuditModel model.UserAuditRecordModelInterface
	scoreModel     *model.UserScoreModel
	cache          *cache.AccountCache
	tokenConfig    TokenConfig
}

func NewAccountService() *AccountService {
	jwtSecret := xlaccount.TestJwtSecret
	if env.IsProd() {
		jwtSecret = xlaccount.ProdJwtSecret
	}
	return &AccountService{
		userModel:      model.NewUserModel(),
		regInfoModel:   model.NewUserRegisterInfoModel(),
		userAuthModel:  model.NewUserAuthModel(),
		userAuditModel: model.NewUserAuditRecordModel(),
		scoreModel:     model.NewUserScoreModel(),
		cache:          cache.NewAccountCache(),
		tokenConfig: TokenConfig{
			jwtSecret:       jwtSecret,
			AccessTokenTTL:  consts.AccessTokenExpire,
			RefreshTokenTTL: consts.RefreshTokenExpire,
			ClockSkew:       consts.ClockSkew,
			MaxRefreshTime:  consts.MaxRefreshTime,
		},
	}
}

// SignUp 用户注册
func (s *AccountService) SignUp(ctx context.Context, req *svcaccount.AccountSignUpReq) (*svcaccount.AccountSignUpResp, error) {
	phone := req.PhoneNumber
	// 检查是否在手机号白名单中，如果在白名单中则跳过验证码验证
	if config.InWhitePhoneList(phone) {
		// 白名单用户直接通过验证
		logger.Infof("phone %s is in whitelist, skip verification code check", phone)
	} else {
		verifyInfo, err := s.verifyCode(ctx, req.GetVerificationToken(), req.SmsCode)
		if err != nil {
			return nil, errcode.ErrInvalidVerifyCode
		}
		if verifyInfo.IsRegistered {
			return nil, errcode.ErrPhoneAlreadyBound
		}
		if verifyInfo.Phone != req.GetPhoneNumber() {
			return nil, errcode.ErrPhoneNotMatch
		}

		if s.IsPhoneBlacklisted(ctx, verifyInfo.Phone) {
			logger.Infof("phone %s is blacklisted", verifyInfo.Phone)
			return nil, errcode.ErrUserBlocked
		}
	}

	user, err := s.createUser(ctx, phone)
	if err != nil {
		logger.Errorf("createUser error: %v", err)
		return nil, errcode.ErrDBInsertFailed
	}

	base := bizcontext.GetBaseContext(ctx)
	tokenInfo, err := s.generateAuthTokens(ctx, user.UserId, base.GetDid(), base.GetDt())
	if err != nil {
		logger.Errorf("generateAuthTokens error: %v", err)
		return nil, errcode.ErrTokenCreateFailed
	}

	// 迅数上报
	s.ReportSignUp(ctx, user, consts.AuthTypePhone)

	// 注册事件
	if err := event.PushSignupData(event.SignupData{
		UserId:       user.UserId,
		Nickname:     user.Nickname,
		App:          base.GetApp(),
		Version:      base.GetAv(),
		Dt:           base.GetDt(),
		Channel:      base.GetCh(),
		Did:          base.GetDid(),
		AuthType:     int32(consts.AuthTypePhone),
		RegisterTime: user.CreatedAt,
	}); err != nil {
		logger.Errorf("PushSignupData error: %v", err)
	}

	return &svcaccount.AccountSignUpResp{
		Data: &svcaccount.TokenRespData{
			TokenType:    xlaccount.BearerType,
			AccessToken:  tokenInfo.AccessToken,
			RefreshToken: tokenInfo.RefreshToken,
			ExpiresIn:    tokenInfo.ExpiresIn,
			UserId:       user.UserId,
			NickName:     user.Nickname,
			Avatar:       alioss.FillCharacterAvatarUrl(user.Avatar),
			Gender:       user.Gender,
		},
	}, nil
}

// SignIn 用户登录
func (s *AccountService) SignIn(ctx context.Context, phone, verificationToken, code, deviceId string, platform int32) (*AuthTokenInfo, *model.User, error) {
	// 检查是否在手机号白名单中，如果在白名单中则跳过验证码验证
	if config.InWhitePhoneList(phone) {
		// 白名单用户直接通过验证
		logger.Infof("phone %s is in whitelist, skip verification code check", phone)
	} else {
		// 非白名单用户需要验证码
		verifyInfo, err := s.verifyCode(ctx, verificationToken, code)
		if err != nil {
			return nil, nil, errcode.ErrInvalidVerifyCode
		}
		if verifyInfo.Phone != phone {
			return nil, nil, errcode.ErrPhoneNotMatch
		}
	}

	lock := s.cache.GetDistributedLock(fmt.Sprintf(cache.KeySignInLock, phone))
	if lock == nil {
		return nil, nil, errcode.ErrTooManyRequests
	}
	defer lock.Release()

	user, err := s.GetActiveUserByPhone(ctx, phone)
	if err != nil {
		return nil, nil, errcode.ErrUserNotFound
	}

	if s.IsBlacklisted(ctx, user.UserId) {
		logger.Infof("user %d is blacklisted", user.UserId)
		return nil, nil, errcode.ErrUserBlocked
	}

	if user.Status != consts.UserStatusNormal {
		return nil, nil, errcode.ErrUserBlocked
	}

	tokenInfo, err := s.generateAuthTokens(ctx, user.UserId, deviceId, platform)
	if err != nil {
		return nil, nil, err
	}

	return tokenInfo, user, nil
}

// generateAuthTokens 生成认证Token对，支持单设备登录
func (s *AccountService) generateAuthTokens(ctx context.Context, userId int64, deviceId string, platform int32) (*AuthTokenInfo, error) {
	// 检查用户是否已在其他设备登录
	existingLogin, err := s.cache.GetUserLogin(ctx, userId)
	if err != nil {
		logger.Errorf("get user login failed: %v", err)
	}

	// 如果用户已在其他设备登录，踢下线
	if existingLogin != nil && existingLogin.DeviceId != deviceId {
		logger.Infof("user %d login from new device %s, kick off old device %s", userId, deviceId, existingLogin.DeviceId)

		// 将旧设备的所有token加入黑名单
		// 1. 将refresh token加入黑名单
		refreshTTL := s.calculateTokenRemainingTTL(existingLogin.RefreshTokenId, s.tokenConfig.RefreshTokenTTL)
		if err := s.cache.AddTokenToBlacklist(ctx, existingLogin.RefreshTokenId, refreshTTL); err != nil {
			logger.Errorf("add refresh token to blacklist failed: %v", err)
		}

		// 2. 将对应的access token也加入黑名单
		if existingLogin.AccessTokenId != "" {
			accessTTL := s.calculateTokenRemainingTTL(existingLogin.AccessTokenId, s.tokenConfig.AccessTokenTTL)
			if err := s.cache.AddTokenToBlacklist(ctx, existingLogin.AccessTokenId, accessTTL); err != nil {
				logger.Errorf("add access token to blacklist failed: %v", err)
			}
		}

		// 删除旧的登录状态
		if err := s.cache.DeleteUserLogin(ctx, userId); err != nil {
			logger.Errorf("delete user login failed: %v", err)
		}
	}

	// 生成访问令牌
	accessToken, err := s.createToken(ctx, userId, deviceId, consts.TokenTypeAccess, platform, s.tokenConfig.AccessTokenTTL)
	if err != nil {
		return nil, errcode.ErrTokenCreateFailed.WithMessage("create access token failed")
	}

	// 生成刷新令牌
	refreshToken, err := s.createToken(ctx, userId, deviceId, consts.TokenTypeRefresh, platform, s.tokenConfig.RefreshTokenTTL)
	if err != nil {
		return nil, errcode.ErrTokenCreateFailed.WithMessage("create refresh token failed")
	}

	// 解析 refresh token 获取 token ID
	refreshClaims, err := s.parseTokenWithoutValidation(refreshToken)
	if err != nil {
		return nil, errcode.ErrTokenCreateFailed.WithMessage("parse refresh token failed")
	}

	// 解析 access token 获取 token ID
	accessClaims, err := s.parseTokenWithoutValidation(accessToken)
	if err != nil {
		return nil, errcode.ErrTokenCreateFailed.WithMessage("parse access token failed")
	}

	// 获取客户端IP和User-Agent
	base := bizcontext.GetBaseContext(ctx)
	ipAddress := ""
	if base != nil {
		ipAddress = base.GetIp()
	}

	// 保存新的登录状态
	loginInfo := &cache.UserLoginInfo{
		UserId:         userId,
		DeviceId:       deviceId,
		RefreshTokenId: refreshClaims.ID,
		AccessTokenId:  accessClaims.ID,
		Platform:       platform,
		Fingerprint:    s.generateDeviceFingerprint(deviceId, platform),
		LoginTime:      time.Now().Unix(),
		LastActive:     time.Now().Unix(),
		IPAddress:      ipAddress,
	}

	if err := s.cache.SetUserLogin(ctx, loginInfo); err != nil {
		logger.Errorf("set user login failed: %v", err)
	}

	// 添加设备登录历史
	deviceHistory := &cache.DeviceLoginHistory{
		DeviceId:    deviceId,
		Platform:    platform,
		Fingerprint: loginInfo.Fingerprint,
		LoginTime:   time.Now().Unix(),
		IPAddress:   ipAddress,
	}

	if err := s.cache.AddDeviceHistory(ctx, userId, deviceHistory); err != nil {
		logger.Errorf("add device history failed: %v", err)
	}

	return &AuthTokenInfo{
		UserId:       userId,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(s.tokenConfig.AccessTokenTTL.Seconds()),
	}, nil
}

// RefreshToken 刷新Token
func (s *AccountService) RefreshToken(ctx context.Context, refreshToken, deviceId string) (*AuthTokenInfo, error) {
	claims, err := s.verifyToken(ctx, refreshToken)
	if err != nil {
		return nil, err
	}

	if s.IsBlacklisted(ctx, claims.UserId) {
		logger.Infof("user %d is blacklisted", claims.UserId)
		return nil, errcode.ErrUserBlocked
	}

	if claims.TokenType != consts.TokenTypeRefresh {
		return nil, errcode.ErrTokenTypeMismatch
	}

	// 检查refresh token是否过期太久
	if time.Since(claims.IssuedAt.Time) > s.tokenConfig.MaxRefreshTime {
		return nil, errcode.ErrTokenTooOld.WithMessage("请重新登录以保障账号安全")
	}

	// 验证设备ID
	if claims.DeviceId != deviceId {
		return nil, errcode.ErrDeviceMismatch
	}

	// 生成新的token对
	return s.generateAuthTokens(ctx, claims.UserId, deviceId, claims.Platform)
}

// generateDeviceFingerprint 生成增强的设备指纹
func (s *AccountService) generateDeviceFingerprint(deviceId string, platform int32) string {
	if deviceId == "" {
		return ""
	}

	// 组合多个因素生成更安全的设备指纹
	h := sha256.New()
	h.Write([]byte(fmt.Sprintf("%s_%d_%s", deviceId, platform, s.tokenConfig.jwtSecret)))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// validateDeviceFingerprint 验证设备指纹
func (s *AccountService) validateDeviceFingerprint(claims *JWTClaims) bool {
	expectedFingerprint := s.generateDeviceFingerprint(claims.DeviceId, claims.Platform)
	return claims.Fingerprint == expectedFingerprint
}

// createToken 创建JWT Token
func (s *AccountService) createToken(ctx context.Context, userId int64, deviceId string, tokenType consts.TokenType, platform int32, expiration time.Duration) (string, error) {
	claims := JWTClaims{
		UserId:      userId,
		DeviceId:    deviceId,
		TokenType:   tokenType,
		Platform:    platform,
		Fingerprint: s.generateDeviceFingerprint(deviceId, platform),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expiration)),
			NotBefore: jwt.NewNumericDate(time.Now().Add(-s.tokenConfig.ClockSkew)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			ID:        fmt.Sprintf("%d_%s_%d", userId, deviceId, time.Now().UnixNano()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)
	return token.SignedString([]byte(s.tokenConfig.jwtSecret))
}

// verifyToken 验证JWT Token
func (s *AccountService) verifyToken(ctx context.Context, tokenString string) (*JWTClaims, error) {
	if tokenString == "" {
		return nil, errcode.ErrTokenRequired
	}

	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errcode.ErrInvalidSigningMethod.WithMessage(fmt.Sprintf("unexpected signing method: %v", token.Header["alg"]))
		}
		return []byte(s.tokenConfig.jwtSecret), nil
	})

	if err != nil {
		var ve *jwt.ValidationError
		if errors.As(err, &ve) {
			if ve.Errors&jwt.ValidationErrorExpired != 0 {
				return nil, errcode.ErrTokenExpired
			}
		}
		return nil, errcode.ErrInvalidToken.WithMessage(err.Error())
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, errcode.ErrInvalidToken
	}

	// 检查Token是否在黑名单中
	if s.cache.IsTokenInBlacklist(ctx, claims.ID) {
		return nil, errcode.ErrTokenInvalid.WithMessage("token已被禁用")
	}

	// 验证设备指纹
	if !s.validateDeviceFingerprint(claims) {
		return nil, errcode.ErrDeviceFingerprint
	}

	// 检查用户是否在黑名单中
	if s.IsBlacklisted(ctx, claims.UserId) {
		return nil, errcode.ErrUserBlocked
	}

	// 更新用户最后活跃时间
	if err := s.cache.UpdateUserLastActive(ctx, claims.UserId); err != nil {
		logger.Errorf("update user last active failed: %v", err)
	}

	return claims, nil
}

// parseTokenWithoutValidation 解析token但不验证过期时间等（内部方法）
func (s *AccountService) parseTokenWithoutValidation(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errcode.ErrInvalidSigningMethod.WithMessage(fmt.Sprintf("unexpected signing method: %v", token.Header["alg"]))
		}
		return []byte(s.tokenConfig.jwtSecret), nil
	})

	if err != nil {
		return nil, err
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return nil, errcode.ErrInvalidToken
	}

	return claims, nil
}

// calculateTokenRemainingTTL 计算token的剩余有效期
// 注意：这里传入的是tokenId，我们需要从tokenId中解析时间戳来估算剩余时间
func (s *AccountService) calculateTokenRemainingTTL(tokenId string, defaultTTL time.Duration) time.Duration {
	// tokenId格式：userId_deviceId_timestamp
	// 从tokenId中提取时间戳来估算token的创建时间
	parts := strings.Split(tokenId, "_")
	if len(parts) >= 3 {
		if timestamp, err := strconv.ParseInt(parts[2], 10, 64); err == nil {
			// 将纳秒时间戳转换为时间
			createdAt := time.Unix(0, timestamp)
			// 计算token的过期时间
			expiresAt := createdAt.Add(defaultTTL)
			// 计算剩余时间
			remaining := time.Until(expiresAt)
			if remaining > 0 {
				return remaining
			}
		}
	}

	// 如果无法解析，使用默认TTL确保安全
	return defaultTTL
}

// VerifyTokenWithContext 验证Token（包括黑名单检查）- 供RPC调用使用
func (s *AccountService) VerifyTokenWithContext(ctx context.Context, tokenString, deviceId string, platform int32, userAgent string) (*JWTClaims, error) {
	if tokenString == "" {
		return nil, errcode.ErrTokenRequired
	}

	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errcode.ErrInvalidSigningMethod.WithMessage(fmt.Sprintf("unexpected signing method: %v", token.Header["alg"]))
		}
		return []byte(s.tokenConfig.jwtSecret), nil
	})

	if err != nil {
		var ve *jwt.ValidationError
		if errors.As(err, &ve) {
			if ve.Errors&jwt.ValidationErrorExpired != 0 {
				return nil, errcode.ErrTokenExpired
			}
		}
		return nil, errcode.ErrInvalidToken.WithMessage(err.Error())
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, errcode.ErrInvalidToken
	}

	// 检查Token是否在黑名单中
	if s.cache.IsTokenInBlacklist(ctx, claims.ID) {
		return nil, errcode.ErrTokenInvalid.WithMessage("token已被禁用")
	}

	// 验证设备指纹（如果提供了userAgent）
	if userAgent != "" {
		expectedFingerprint := s.generateDeviceFingerprint(claims.DeviceId, claims.Platform)
		if claims.Fingerprint != expectedFingerprint {
			return nil, errcode.ErrDeviceFingerprint
		}
	}

	// 验证设备ID（如果提供了）
	if deviceId != "" && claims.DeviceId != deviceId {
		return nil, errcode.ErrDeviceMismatch
	}

	// 检查用户是否在黑名单中
	if s.IsBlacklisted(ctx, claims.UserId) {
		return nil, errcode.ErrUserBlocked
	}

	return claims, nil
}

// ParseTokenWithoutValidation 解析token但不验证过期时间等（公开方法）
func (s *AccountService) ParseTokenWithoutValidation(tokenString string) (*JWTClaims, error) {
	return s.parseTokenWithoutValidation(tokenString)
}

// SignOut 用户退出登录
func (s *AccountService) SignOut(ctx context.Context, userId int64, tokenId string) error {
	// 获取用户当前登录状态，以便将所有token加入黑名单
	loginInfo, err := s.cache.GetUserLogin(ctx, userId)
	if err == nil && loginInfo != nil {
		// 将refresh token加入黑名单
		refreshTTL := s.calculateTokenRemainingTTL(loginInfo.RefreshTokenId, s.tokenConfig.RefreshTokenTTL)
		if err := s.cache.AddTokenToBlacklist(ctx, loginInfo.RefreshTokenId, refreshTTL); err != nil {
			logger.Errorf("add refresh token to blacklist failed: %v", err)
		}

		// 将access token加入黑名单
		if loginInfo.AccessTokenId != "" {
			accessTTL := s.calculateTokenRemainingTTL(loginInfo.AccessTokenId, s.tokenConfig.AccessTokenTTL)
			if err := s.cache.AddTokenToBlacklist(ctx, loginInfo.AccessTokenId, accessTTL); err != nil {
				logger.Errorf("add access token to blacklist failed: %v", err)
			}
		}
	} else {
		// 如果无法获取登录信息，至少将传入的token加入黑名单
		refreshTTL := s.calculateTokenRemainingTTL(tokenId, s.tokenConfig.RefreshTokenTTL)
		if err := s.cache.AddTokenToBlacklist(ctx, tokenId, refreshTTL); err != nil {
			logger.Errorf("add token to blacklist failed: %v", err)
			return err
		}
	}

	// 删除用户登录状态
	if err := s.cache.DeleteUserLogin(ctx, userId); err != nil {
		logger.Errorf("delete user login failed: %v", err)
		return err
	}

	// 清除用户信息缓存
	if err := s.cache.DeleteUserInfo(ctx, userId); err != nil {
		logger.Errorf("delete user info cache failed: %v", err)
	}

	logger.Infof("user %d signed out successfully", userId)
	return nil
}

// KickOffDevice 踢下线指定设备
func (s *AccountService) KickOffDevice(ctx context.Context, userId int64, deviceId string) error {
	// 获取用户当前登录状态
	loginInfo, err := s.cache.GetUserLogin(ctx, userId)
	if err != nil {
		return err
	}

	if loginInfo == nil {
		return errcode.ErrUserNotLoggedIn
	}

	// 检查是否是指定设备
	if loginInfo.DeviceId != deviceId {
		return errcode.ErrDeviceNotFound
	}

	// 将refresh token加入黑名单
	refreshTTL := s.calculateTokenRemainingTTL(loginInfo.RefreshTokenId, s.tokenConfig.RefreshTokenTTL)
	if err := s.cache.AddTokenToBlacklist(ctx, loginInfo.RefreshTokenId, refreshTTL); err != nil {
		logger.Errorf("add refresh token to blacklist failed: %v", err)
		return err
	}

	// 将access token加入黑名单
	if loginInfo.AccessTokenId != "" {
		accessTTL := s.calculateTokenRemainingTTL(loginInfo.AccessTokenId, s.tokenConfig.AccessTokenTTL)
		if err := s.cache.AddTokenToBlacklist(ctx, loginInfo.AccessTokenId, accessTTL); err != nil {
			logger.Errorf("add access token to blacklist failed: %v", err)
			return err
		}
	}

	// 删除登录状态
	if err := s.cache.DeleteUserLogin(ctx, userId); err != nil {
		logger.Errorf("delete user login failed: %v", err)
		return err
	}

	logger.Infof("kicked off device %s for user %d", deviceId, userId)
	return nil
}

// GetUserLoginStatus 获取用户登录状态
func (s *AccountService) GetUserLoginStatus(ctx context.Context, userId int64) (*cache.UserLoginInfo, error) {
	return s.cache.GetUserLogin(ctx, userId)
}

// GetUserDeviceHistory 获取用户设备登录历史
func (s *AccountService) GetUserDeviceHistory(ctx context.Context, userId int64) ([]*cache.DeviceLoginHistory, error) {
	return s.cache.GetDeviceHistory(ctx, userId)
}

func (s *AccountService) StoreVerificationInfo(ctx context.Context, info *cache.VerificationTokenInfo, token string) error {
	return s.cache.SetVerificationToken(ctx, token, info)
}

func (s *AccountService) verifyCode(ctx context.Context, token, code string) (*cache.VerificationTokenInfo, error) {
	info, err := s.cache.GetVerificationToken(ctx, token)
	if err != nil {
		return nil, errors.New("invalid verification token")
	}

	if info.ExpireAt < time.Now().Unix() {
		return nil, errors.New("verification code expired")
	}

	if info.Attempts >= consts.MaxVerifyAttempts {
		return nil, errors.New("too many attempts")
	}

	if err := s.cache.IncrVerificationAttempts(ctx, token); err != nil {
		return nil, err
	}

	if info.Code != code {
		return nil, errors.New("invalid verification code")
	}

	_ = s.cache.DeleteVerificationToken(ctx, token)

	return info, nil
}

func (s *AccountService) GetUserByPhone(ctx context.Context, phone string) (*model.User, error) {
	user, err := s.userModel.GetUserByPhone(ctx, phone)
	if err != nil {
		return nil, err
	}
	if user.Avatar != "" {
		user.Avatar = user.GetFullCharacterAvatarURL()
	}
	return user, nil
}

// GetActiveUserByPhone 获取未删除的用户（专门用于登录验证）
func (s *AccountService) GetActiveUserByPhone(ctx context.Context, phone string) (*model.User, error) {
	user, err := s.userModel.GetActiveUserByPhone(ctx, phone)
	if err != nil {
		return nil, err
	}
	if user.Avatar != "" {
		user.Avatar = user.GetFullCharacterAvatarURL()
	}
	return user, nil
}

func (s *AccountService) GetUserByUserId(ctx context.Context, userID int64) (*model.User, error) {
	user, err := s.userModel.GetUserByUserId(ctx, userID)
	if err != nil {
		return nil, err
	}
	if user.Avatar != "" {
		user.Avatar = user.GetFullCharacterAvatarURL()
	}
	return user, nil
}

func (s *AccountService) createUser(ctx context.Context, phone string) (*model.User, error) {
	now := util.NowTimeMillis()
	user := &model.User{
		Nickname:       config.GetDefaultNickname(),
		NicknamePinyin: config.GetDefaultNickname(),
		Phone:          phone,
		Avatar:         config.GetDefaultAvatar(),
		Status:         consts.UserStatusNormal,
		BackgroundUrl:  config.GetDefaultBg(),
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	// 启动事务处理
	var createdUser *model.User
	err := s.userModel.(*model.UserModel).DB.Transaction(func(tx *gorm.DB) error {
		// 1. 创建用户基本信息
		if err := s.userModel.CreateUserWithTx(ctx, tx, user); err != nil {
			logger.Errorf("create user failed: %v", err)
			return err
		}

		// 1.1 更新名称
		nickname := fmt.Sprintf("%s%d", config.GetDefaultNickname(), user.UserId)
		user.Nickname = nickname
		user.NicknamePinyin = util.ConvertToPinyin(nickname)
		if err := s.userModel.UpdateUserWithTx(ctx, tx, user.UserId, map[string]interface{}{
			"nickname":        user.Nickname,
			"nickname_pinyin": user.NicknamePinyin,
		}); err != nil {
			logger.Errorf("update user nickname failed: %v", err)
			return err
		}

		// 2. 创建用户认证信息
		auth := &model.UserAuth{
			UserId:    user.UserId,
			AuthType:  uint32(consts.AuthTypePhone),
			AuthID:    user.Phone,
			Status:    consts.AuthStatusNormal,
			CreatedAt: now,
			UpdatedAt: now,
		}
		if err := s.userAuthModel.CreateUserAuthWithTx(ctx, tx, auth); err != nil {
			return err
		}

		// 3. 创建用户注册信息
		base := bizcontext.GetBaseContext(ctx)
		regInfo := &model.UserRegisterInfo{
			UserID:  user.UserId,
			Gender:  int8(user.Gender),
			AppPkg:  base.GetApp(),
			AppVer:  base.GetAv(),
			DevType: uint8(base.GetDt()),
			DevID:   base.GetDid(),
			NetType: uint8(base.GetNt()),
			Channel: base.GetCh(),
			Brand:   base.GetBd(),
			Model:   lo.Substring(base.GetMd(), 0, 100),
			OS:      base.GetOs(),
			CT:      now,
			AID:     base.GetOaid(),
			IMEI:    base.GetImei(),
			OAID:    base.GetOaid(),
			IDFA:    base.GetIdfa(),
		}
		if err := s.regInfoModel.CreateWithTx(ctx, tx, regInfo); err != nil {
			return err
		}

		createdUser = user
		return nil
	})

	if err != nil {
		logger.Errorf("createUser transaction failed: %v", err)
		return nil, errcode.ErrUserCreateFailed
	}

	return createdUser, nil
}

func (s *AccountService) genRandomNickName(ctx context.Context) (string, error) {
	var nickname string
	maxRetries := 100

	for i := 0; i < maxRetries; i++ {
		nickname = fmt.Sprintf("%s%04d", config.GetDefaultNickname(), time.Now().UnixNano()%10000)

		existingUser, err := s.userModel.ExistsNickname(ctx, nickname)
		if err != nil {
			return "", errors.New("invalid nickname")
		}
		if !existingUser {
			return nickname, nil
		}
	}
	return "", errors.New("invalid nickname")
}

func (s *AccountService) CheckPhone(phone string) bool {
	return !regexp.MustCompile(`^1\d{10}$`).MatchString(phone)
}

func (s *AccountService) DeleteAccount(ctx context.Context, userId int64) error {
	lock := s.cache.GetDistributedLock(fmt.Sprintf(cache.KeyDeleteLock, userId))
	if lock == nil {
		return errcode.ErrTooManyRequests
	}
	defer lock.Release()

	// 清理用户登录状态和token（确保账户注销后无法继续使用token）
	loginInfo, err := s.cache.GetUserLogin(ctx, userId)
	if err == nil && loginInfo != nil {
		// 将所有token加入黑名单
		refreshTTL := s.calculateTokenRemainingTTL(loginInfo.RefreshTokenId, s.tokenConfig.RefreshTokenTTL)
		if err := s.cache.AddTokenToBlacklist(ctx, loginInfo.RefreshTokenId, refreshTTL); err != nil {
			logger.Errorf("add refresh token to blacklist failed: %v", err)
		}

		if loginInfo.AccessTokenId != "" {
			accessTTL := s.calculateTokenRemainingTTL(loginInfo.AccessTokenId, s.tokenConfig.AccessTokenTTL)
			if err := s.cache.AddTokenToBlacklist(ctx, loginInfo.AccessTokenId, accessTTL); err != nil {
				logger.Errorf("add access token to blacklist failed: %v", err)
			}
		}

		// 删除登录状态
		if err := s.cache.DeleteUserLogin(ctx, userId); err != nil {
			logger.Errorf("delete user login failed: %v", err)
		}
	}

	if err := s.userModel.DeleteUser(ctx, userId); err != nil {
		return err
	}

	if err := s.cache.DeleteUserInfo(ctx, userId); err != nil {
		logger.Errorf("delete user info cache failed: %v", err)
	}

	//注销上报
	s.ReportDeleteAccount(ctx, userId)

	return nil
}

// GetUserInfo 获取用户信息
func (s *AccountService) GetUserInfo(ctx context.Context, userId int64) (*cache.UserInfo, error) {
	// 1. 先尝试从缓存获取
	userInfo, err := s.cache.GetUserInfo(ctx, userId)
	if err == nil && userInfo != nil {
		userInfo.BackgroundUrl = alioss.FillImageUrl(userInfo.BackgroundUrl)
		userInfo.Avatar = alioss.FillCharacterAvatarUrl(userInfo.Avatar)
		userInfo.VoiceSignatureUrl = alioss.FillAudioUrl(userInfo.VoiceSignatureUrl)
		return userInfo, nil
	}

	// 2. 缓存未命中，从数据库查询
	user, err := s.userModel.GetUserByUserId(ctx, userId)
	if err != nil {
		return nil, errcode.ErrUserNotFound
	}

	// 3. 构建用户信息
	userInfo = &cache.UserInfo{
		UserId:            user.UserId,
		Nickname:          user.Nickname,
		Phone:             user.Phone,
		Avatar:            user.GetFullCharacterAvatarURL(),
		Gender:            user.Gender,
		Status:            user.Status,
		CreatedAt:         user.CreatedAt,
		BackgroundUrl:     user.GetFullBackgroundUrl(),
		VoiceSignatureUrl: user.GetFullVoiceSignatureUrl(),
		VoiceDuration:     user.VoiceDuration,
		IsPremiumCreator:  user.IsPremiumCreator,
		IsDeleted:         user.DeletedAt > 0,
		CreationUserId:    user.CreationUserId,
	}

	// 4. 异步设置缓存，避免影响响应时间
	go func() {
		if err := s.cache.SetUserInfo(context.Background(), userInfo); err != nil {
			logger.Errorf("set user info cache failed: %v", err)
		}
	}()

	return userInfo, nil
}

// GetUserInfoByCreationUserId 根据admin用户ID获取关联的业务用户信息
func (s *AccountService) GetUserInfoByCreationUserId(ctx context.Context, creationUserId uint) (*cache.UserInfo, error) {
	// 从数据库查询关联的业务用户
	user, err := s.userModel.GetUserByCreationUserId(ctx, creationUserId)
	if err != nil {
		logger.Errorf("GetUserByCreationUserId failed: creationUserId=%d, error=%v", creationUserId, err)
		return nil, errcode.ErrUserNotFound
	}

	logger.Infof("GetUserByCreationUserId success: creationUserId=%d, userId=%d", creationUserId, user.UserId)

	// 构建用户信息
	userInfo := &cache.UserInfo{
		UserId:            user.UserId,
		Nickname:          user.Nickname,
		Phone:             user.Phone,
		Avatar:            user.GetFullCharacterAvatarURL(),
		Gender:            user.Gender,
		Status:            user.Status,
		CreatedAt:         user.CreatedAt,
		BackgroundUrl:     user.GetFullBackgroundUrl(),
		VoiceSignatureUrl: user.GetFullVoiceSignatureUrl(),
		VoiceDuration:     user.VoiceDuration,
		IsPremiumCreator:  user.IsPremiumCreator,
		IsDeleted:         user.DeletedAt > 0,
		CreationUserId:    user.CreationUserId,
	}

	return userInfo, nil
}

// BatchGetUserInfo 批量获取用户信息
func (s *AccountService) BatchGetUserInfo(ctx context.Context, userIDs []int64) (map[int64]*svcaccount.UserInfo, error) {
	if len(userIDs) == 0 {
		return map[int64]*svcaccount.UserInfo{}, nil
	}

	// 1. 先从缓存批量获取
	cachedUsers, missedIDs, err := s.cache.BatchGetUserInfo(ctx, userIDs)
	if err != nil {
		logger.Errorf("BatchGetUserInfo from cache failed: %v", err)
		// 缓存失败时直接查数据库
		missedIDs = userIDs
		cachedUsers = make(map[int64]*cache.UserInfo)
	}

	result := make(map[int64]*svcaccount.UserInfo)

	// 2. 转换缓存中的用户信息
	for userId, userInfo := range cachedUsers {
		userResp := &svcaccount.UserInfo{
			UserId:        userInfo.UserId,
			Nickname:      userInfo.Nickname,
			Phone:         userInfo.Phone,
			Avatar:        alioss.FillImageUrl(userInfo.Avatar),
			Gender:        userInfo.Gender,
			Status:        int32(userInfo.Status),
			CreatedAt:     userInfo.CreatedAt,
			BackgroundUrl: alioss.FillImageUrl(userInfo.BackgroundUrl),
			VoiceSignature: &svcaccount.VoiceSignature{
				VoiceSignatureUrl: alioss.FillAudioUrl(userInfo.VoiceSignatureUrl),
				Duration:          userInfo.VoiceDuration,
			},
			IsPremiumCreator: userInfo.IsPremiumCreator,
			IsDeleted:        userInfo.IsDeleted,
		}

		// 如果用户已注销，进行特殊处理
		if userInfo.IsDeleted {
			userResp.Avatar = config.GetDefaultAvatar()
			userResp.BackgroundUrl = config.GetDefaultBg()
			userResp.Nickname = fmt.Sprintf("用户已注销%d", userInfo.UserId)
			userResp.VoiceSignature = &svcaccount.VoiceSignature{
				VoiceSignatureUrl: "",
				Duration:          0,
				IsReviewing:       false,
			}
		}

		result[userId] = userResp
	}

	// 3. 查询缓存未命中的用户
	if len(missedIDs) > 0 {
		users, err := s.userModel.GetUsersByIDs(ctx, missedIDs)
		if err != nil {
			logger.Errorf("Failed to get users by IDs: %v", err)
			return result, nil // 返回已有的缓存结果
		}

		// 4. 构建缓存数据和响应数据
		cacheData := make(map[int64]*cache.UserInfo)
		for _, user := range users {
			// 判断用户是否已注销
			isDeleted := user.DeletedAt > 0

			// 构建缓存数据
			userInfo := &cache.UserInfo{
				UserId:            user.UserId,
				Nickname:          user.Nickname,
				Phone:             user.Phone,
				Avatar:            user.GetFullCharacterAvatarURL(),
				Gender:            user.Gender,
				Status:            user.Status,
				CreatedAt:         user.CreatedAt,
				BackgroundUrl:     user.GetFullBackgroundUrl(),
				VoiceSignatureUrl: user.GetFullVoiceSignatureUrl(),
				VoiceDuration:     user.VoiceDuration,
				IsPremiumCreator:  user.IsPremiumCreator,
				IsDeleted:         isDeleted,
				CreationUserId:    user.CreationUserId,
			}
			cacheData[user.UserId] = userInfo

			// 构建响应数据
			userResp := &svcaccount.UserInfo{
				UserId:        user.UserId,
				Nickname:      user.Nickname,
				Phone:         user.Phone,
				Avatar:        user.GetFullCharacterAvatarURL(),
				Gender:        user.Gender,
				Status:        int32(user.Status),
				CreatedAt:     user.CreatedAt,
				BackgroundUrl: user.GetFullBackgroundUrl(),
				VoiceSignature: &svcaccount.VoiceSignature{
					VoiceSignatureUrl: user.GetFullVoiceSignatureUrl(),
					Duration:          user.VoiceDuration,
				},
				IsPremiumCreator: user.IsPremiumCreator,
				IsDeleted:        isDeleted,
			}

			// 如果用户已注销，进行特殊处理
			if isDeleted {
				userResp.Avatar = config.GetDefaultAvatar()
				userResp.BackgroundUrl = config.GetDefaultBg()
				userResp.Nickname = fmt.Sprintf("用户已注销%d", user.UserId)
				userResp.VoiceSignature = &svcaccount.VoiceSignature{
					VoiceSignatureUrl: "",
					Duration:          0,
					IsReviewing:       false,
				}
			}

			result[user.UserId] = userResp
		}

		// 5. 批量设置缓存
		if len(cacheData) > 0 {
			if err := s.cache.BatchSetUserInfo(ctx, cacheData); err != nil {
				logger.Errorf("BatchSetUserInfo to cache failed: %v", err)
			}
		}
	}

	return result, nil
}

// SearchUsers 搜索用户
func (s *AccountService) SearchUsers(ctx context.Context, keyword string, page, pageSize int32) ([]*svcaccount.UserInfo, int64, error) {
	if keyword == "" {
		return nil, 0, errcode.ErrorParam
	}

	users, total, err := s.userModel.SearchUsers(ctx, keyword, page, pageSize)
	if err != nil {
		logger.Errorf("Failed to search users: %v", err)
		return nil, 0, err
	}

	result := make([]*svcaccount.UserInfo, 0, len(users))
	for _, user := range users {
		if user.Status != consts.UserStatusNormal {
			continue // 过滤非正常状态的用户
		}

		// 判断用户是否已注销
		isDeleted := user.DeletedAt > 0

		userResp := &svcaccount.UserInfo{
			UserId:        user.UserId,
			Nickname:      user.Nickname,
			Phone:         user.Phone,
			Avatar:        alioss.FillCharacterAvatarUrl(user.Avatar),
			Gender:        user.Gender,
			Status:        int32(user.Status),
			CreatedAt:     user.CreatedAt,
			BackgroundUrl: alioss.FillImageUrl(user.BackgroundUrl),
			VoiceSignature: &svcaccount.VoiceSignature{
				VoiceSignatureUrl: user.GetFullVoiceSignatureUrl(),
				Duration:          user.VoiceDuration,
			},
			IsPremiumCreator: user.IsPremiumCreator,
			IsDeleted:        isDeleted,
		}

		// 如果用户已注销，进行特殊处理
		if isDeleted {
			userResp.Avatar = config.GetDefaultAvatar()
			userResp.BackgroundUrl = config.GetDefaultBg()
			userResp.Nickname = fmt.Sprintf("用户已注销%d", user.UserId)
			userResp.VoiceSignature = &svcaccount.VoiceSignature{
				VoiceSignatureUrl: "",
				Duration:          0,
				IsReviewing:       false,
			}
		}

		result = append(result, userResp)
	}

	return result, total, nil
}

// UpdateUser 更新用户信息
func (s *AccountService) UpdateUser(ctx context.Context, userId int64, req *svcaccount.UpdateUserInfoReq) error {
	userInfo, err := s.userModel.GetUserByUserId(ctx, userId)
	if err != nil {
		logger.Errorf("GetUserByUserId error: %v", err)
		return errcode.ErrUserNotFound
	}
	if userInfo == nil {
		return errcode.ErrUserNotFound
	}

	oldName := userInfo.Nickname
	updates := map[string]any{
		"updated_at": util.NowTimeMillis(),
	}

	// 处理重置语音签名
	if req.ResetVoiceSignature {
		updates["voice_signature_url"] = ""
		updates["voice_duration"] = 0

		// 删除正在审核的语音签名记录
		err := s.userAuditModel.SoftDeletePendingAuditRecords(ctx, userId, model.UserAuditTypeVoice)
		if err != nil {
			logger.Errorf("删除待审核语音签名记录失败: %v, userId: %d", err, userId)
		} else {
			logger.Infof("用户重置语音签名，已删除待审核记录, userId: %d", userId)
		}
	}

	eg, egCtx := errgroup.WithContext(ctx)

	// 审核头像
	if req.Avatar != "" {
		req.Avatar = util.ExtractPath(req.Avatar)
		updates["avatar"] = req.Avatar

		// 创建头像审核记录
		avatarContent := &model.UserAuditContent{
			Avatar:    req.Avatar,
			ReviewUrl: alioss.FillImageUrl(req.Avatar),
		}

		if config.IsAvatarAutoReviewEnabled() {
			eg.Go(func() error {
				// review avatar
				reviewUrl := alioss.FillImageUrl(req.Avatar)
				reviewResp, err := svcmgr.ReviewClient().ReviewMemberInfo(egCtx, &svcreview.ReviewMemberInfoReq{
					Userid:  req.UserId,
					Sex:     userInfo.Gender,
					BizType: svcreview.ReviewBizType_UserAvatar,
					Images:  []*svcreview.ReviewImage{{Url: reviewUrl}},
				})
				logger.Infof("review user avatar, review resp: %v", reviewResp)
				if err != nil {
					logger.Errorf("review user info avatar err: %v", err)
					return err
				}
				if reviewResp.Result != svcreview.AuditResult_pass {
					// 创建机审拒绝的审核记录
					_, err = s.createUserAuditRecord(egCtx, req.UserId, model.UserAuditTypeAvatar, avatarContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED)
					if err != nil {
						logger.Errorf("create avatar audit record failed: %v", err)
						// 不影响主流程，只记录日志
					}
					return errcode.ErrUserAvatarReviewFailed
				}

				// 创建机审通过的审核记录
				_, err = s.createUserAuditRecord(egCtx, req.UserId, model.UserAuditTypeAvatar, avatarContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED)
				if err != nil {
					logger.Errorf("create avatar audit record failed: %v", err)
					// 不影响主流程，只记录日志
				}
				return nil
			})
		} else {
			logger.Infof("头像审核开关已关闭，跳过审核, userId: %d", req.UserId)
			// 创建跳过审核的记录（直接通过）
			_, err := s.createUserAuditRecord(ctx, req.UserId, model.UserAuditTypeAvatar, avatarContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED)
			if err != nil {
				logger.Errorf("create avatar audit record failed: %v", err)
				// 不影响主流程，只记录日志
			}
		}
	}

	// 审核昵称
	if req.Nickname != "" {
		updates["nickname"] = req.Nickname
		updates["nickname_pinyin"] = util.ConvertToPinyin(req.Nickname)

		// 创建昵称审核记录
		nicknameContent := &model.UserAuditContent{
			Nickname: req.Nickname,
		}

		if config.IsNicknameAutoReviewEnabled() {
			eg.Go(func() error {
				// review nickname
				reviewResp, err := svcmgr.ReviewClient().ReviewMemberInfo(egCtx, &svcreview.ReviewMemberInfoReq{
					Userid:  req.UserId,
					Sex:     userInfo.Gender,
					BizType: svcreview.ReviewBizType_UserNickname,
					Text:    []string{req.Nickname},
				})
				logger.Infof("review user nickname, review resp: %v", reviewResp)
				if err != nil {
					logger.Errorf("review user info nickname err: %v", err)
					return err
				}
				if reviewResp.Result != svcreview.AuditResult_pass {
					// 创建机审拒绝的审核记录
					_, err = s.createUserAuditRecord(egCtx, req.UserId, model.UserAuditTypeNickname, nicknameContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED)
					if err != nil {
						logger.Errorf("create nickname audit record failed: %v", err)
						// 不影响主流程，只记录日志
					}
					return errcode.ErrUserNicknameReviewFailed
				}

				// 创建机审通过的审核记录
				_, err = s.createUserAuditRecord(egCtx, req.UserId, model.UserAuditTypeNickname, nicknameContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED)
				if err != nil {
					logger.Errorf("create nickname audit record failed: %v", err)
					// 不影响主流程，只记录日志
				}
				return nil
			})
		} else {
			logger.Infof("昵称审核开关已关闭，跳过审核, userId: %d", req.UserId)
			// 创建跳过审核的记录（直接通过）
			_, err := s.createUserAuditRecord(ctx, req.UserId, model.UserAuditTypeNickname, nicknameContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED)
			if err != nil {
				logger.Errorf("create nickname audit record failed: %v", err)
				// 不影响主流程，只记录日志
			}
		}
	}

	// 审核背景图
	if req.BackgroundUrl != "" {
		req.BackgroundUrl = util.ExtractPath(req.BackgroundUrl)
		updates["background_url"] = req.BackgroundUrl

		// 创建背景图审核记录
		backgroundContent := &model.UserAuditContent{
			BackgroundUrl: req.BackgroundUrl,
			ReviewUrl:     alioss.FillImageUrl(req.BackgroundUrl),
		}

		if config.IsBackgroundAutoReviewEnabled() {
			eg.Go(func() error {
				// review bg
				reviewUrl := alioss.FillImageUrl(req.BackgroundUrl)
				reviewResp, err := svcmgr.ReviewClient().ReviewMemberInfo(egCtx, &svcreview.ReviewMemberInfoReq{
					Userid:  req.UserId,
					Sex:     userInfo.Gender,
					BizType: svcreview.ReviewBizType_UserAlbum,
					Images:  []*svcreview.ReviewImage{{Url: reviewUrl}},
				})
				logger.Infof("review user bg img, review resp: %v", reviewResp)
				if err != nil {
					logger.Errorf("review user info bg img err: %v", err)
					return err
				}
				if reviewResp.Result != svcreview.AuditResult_pass {
					// 创建机审拒绝的审核记录
					_, err = s.createUserAuditRecord(egCtx, req.UserId, model.UserAuditTypeBackground, backgroundContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED)
					if err != nil {
						logger.Errorf("create background audit record failed: %v", err)
					}
					return errcode.ErrUserBackgroundReviewFailed
				}

				// 创建机审通过的审核记录
				_, err = s.createUserAuditRecord(egCtx, req.UserId, model.UserAuditTypeBackground, backgroundContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED)
				if err != nil {
					logger.Errorf("create background audit record failed: %v", err)
					// 不影响主流程，只记录日志
				}
				return nil
			})
		} else {
			logger.Infof("背景图审核开关已关闭，跳过审核, userId: %d", req.UserId)
			// 创建跳过审核的记录（直接通过）
			_, err := s.createUserAuditRecord(ctx, req.UserId, model.UserAuditTypeBackground, backgroundContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED)
			if err != nil {
				logger.Errorf("create background audit record failed: %v", err)
				// 不影响主流程，只记录日志
			}
		}
	}

	// 审核语音签名（只有在不重置签名且提供了新签名时才进行审核）
	if !req.ResetVoiceSignature && req.VoiceSignatureUrl != "" && req.VoiceDuration > 0 {
		req.VoiceSignatureUrl = util.ExtractPath(req.VoiceSignatureUrl)
		// 创建语音签名审核记录
		reviewUrl := alioss.FillAudioUrl(req.VoiceSignatureUrl)
		btId := fmt.Sprintf("%s_%d_%d", svcreview.ReviewBizType_UserSignVoice, req.UserId, util.NowTimeMillis())
		if !env.IsProd() {
			btId = fmt.Sprintf("test_%s_%d_%v", svcreview.ReviewBizType_UserSignVoice, req.UserId, util.NowTimeMillis())
		}

		voiceContent := &model.UserAuditContent{
			VoiceSignatureUrl: req.VoiceSignatureUrl,
			VoiceDuration:     req.VoiceDuration,
			ReviewUrl:         reviewUrl,
			BtId:              btId,
		}

		if config.IsVoiceSignatureAutoReviewEnabled() {
			// 语音签名需要异步审核，创建待审核的记录
			id, err := s.createUserAuditRecord(ctx, req.UserId, model.UserAuditTypeVoice, voiceContent, svcscript.ReviewStatus_REVIEW_STATUS_PENDING)
			if err != nil {
				logger.Errorf("create voice audit record failed: %v", err)
				// 不影响主流程，只记录日志
			}

			eg.Go(func() error {
				// async review voice signature
				reviewResp, err := svcmgr.ReviewClient().ReviewMemberInfo(egCtx, &svcreview.ReviewMemberInfoReq{
					Userid:  req.UserId,
					Sex:     userInfo.Gender,
					BizType: svcreview.ReviewBizType_UserSignVoice,
					Voice: &svcreview.ReviewVoice{
						Id:  btId,
						Url: reviewUrl,
						Sec: int64(req.VoiceDuration / 1000),
					},
					CallbackData: &svcreview.ReviewMemberInfoCallbackData{
						Userid: req.UserId,
						Id:     id,
						Type:   svcreview.ReviewBizType_UserSignVoice,
					},
				})
				logger.Infof("review user voice signature, review resp: %v", reviewResp)
				if err != nil {
					logger.Errorf("review user info voice signature err: %v", err)
					return err
				}
				return nil
			})
		} else {
			// 审核开关关闭，直接更新用户数据
			updates["voice_signature_url"] = req.VoiceSignatureUrl
			updates["voice_duration"] = req.VoiceDuration

			logger.Infof("语音签名审核开关已关闭，跳过审核, userId: %d", req.UserId)
			// 创建跳过审核的记录（直接通过）
			_, err := s.createUserAuditRecord(ctx, req.UserId, model.UserAuditTypeVoice, voiceContent, svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED)
			if err != nil {
				logger.Errorf("create voice audit record failed: %v", err)
				// 不影响主流程，只记录日志
			}
		}
	}

	// 等待所有审核任务完成
	if err := eg.Wait(); err != nil {
		logger.Errorf("审核任务失败: %v", err)
		return err
	}

	// 更新creation_user_id字段
	if req.CreationUserId > 0 {
		updates["creation_user_id"] = uint(req.CreationUserId)
	}

	if len(updates) == 1 {
		return nil
	}

	err = s.userModel.UpdateUser(ctx, userId, updates)
	if err != nil {
		logger.Errorf("UpdateUser error: %v", err)
		return errcode.ErrUserUpdateFailed
	}

	//清除缓存
	err = s.cache.DeleteUserInfo(ctx, userId)
	if err != nil {
		logger.Errorf("delete user info cache failed: %v", err)
	}

	// 修改事件
	if req.Nickname != "" && oldName != req.Nickname {
		err := event.PushEditUserData(&event.EditUserData{
			UserId:   userInfo.UserId,
			Nickname: req.Nickname,
		})
		if err != nil {
			logger.Errorf("PushEditUserData error: %v", err)
		}
	}

	return nil
}

// SetPremiumCreator 设置用户为优质创作者
func (s *AccountService) SetPremiumCreator(ctx context.Context, userID int64, isPremiumCreator bool) error {
	user, err := s.GetUserByUserId(ctx, userID)
	if err != nil {
		logger.Errorf("GetUserByUserId error: %v", err)
		return errcode.ErrUserNotFound
	}
	if user == nil {
		return errcode.ErrUserNotFound
	}

	updates := map[string]any{
		"updated_at":         util.NowTimeMillis(),
		"is_premium_creator": isPremiumCreator,
	}
	err = s.userModel.UpdateUser(ctx, userID, updates)
	if err != nil {
		logger.Errorf("UpdateUser for premium creator error: %v", err)
		return err
	}

	// 清除用户缓存，确保下次获取用户信息时能获取到最新状态
	err = s.cache.DeleteUserInfo(ctx, userID)
	if err != nil {
		logger.Errorf("delete user info cache failed: %v", err)
	}

	return nil
}

// 用户审核回调（语音签名）
func (s *AccountService) ReviewCallback(ctx context.Context, req *svcaccount.ReviewCallbackReq) (*common.SvcCommonResp, error) {
	logger.Infof("ReviewCallback req: %v", req)
	rep := &common.SvcCommonResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
	}

	if req.GetId() <= 0 || req.GetUserid() <= 0 {
		logger.Errorf("invalid request, req: %v", req)
		return rep, nil
	}

	userInfo, err := s.userModel.GetUserByUserId(ctx, req.Userid)
	if err != nil {
		logger.Errorf("GetUserInfo error: %v", err)
		return rep, nil
	}
	if userInfo == nil {
		logger.Errorf("user not found, user is nil: %d", req.Userid)
		return rep, nil
	}

	switch req.GetType() {
	case svcreview.ReviewBizType_UserSignVoice:
		// 检查是否有待审核的语音签名记录
		record, err := s.userAuditModel.GetUserAuditRecordByID(ctx, req.Id)
		if err != nil || record == nil {
			logger.Errorf("user review voice signature record not found, userId: %d, id:%d", req.GetUserid(), req.GetId())
			return rep, nil
		}
		if record.AuditType != model.UserAuditTypeVoice || record.AuditStatus != svcscript.ReviewStatus_REVIEW_STATUS_PENDING {
			logger.Errorf("invalid user review voice signature record, userId: %d, id:%d", req.GetUserid(), req.GetId())
			return rep, nil
		}

		// 检查语音签名审核开关
		if config.IsVoiceSignatureAutoReviewEnabled() {
			// 开关打开，需要人审，只更新审核记录状态为机审通过/拒绝
			var auditStatus svcscript.ReviewStatus
			if req.Result == svcreview.AuditResult_pass {
				auditStatus = svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED
			} else {
				auditStatus = svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED
			}

			updates := map[string]any{
				"audit_status": auditStatus,
			}

			// 如果人审关闭，则直接更新用户信息
			if !config.IsVoiceSignatureManualReviewEnabled() && req.Result == svcreview.AuditResult_pass {
				content, err := record.GetContentStruct()
				if err == nil {
					updates["voice_signature_url"] = content.VoiceSignatureUrl
					updates["voice_duration"] = content.VoiceDuration
				}
			}

			// 查找对应的审核记录并更新状态
			// 更新审核记录状态
			err = s.userAuditModel.UpdateUserAuditRecord(ctx, record.ID, updates)
			if err != nil {
				logger.Errorf("update audit record status failed: %v", err)
			} else {
				logger.Infof("updated audit record status: recordId=%d, status=%d", record.ID, auditStatus)
			}

			// 如果机审拒绝，发送通知
			if req.Result != svcreview.AuditResult_pass {
				// 发送审核未通过通知
				s.NotifyVoiceSignatureReviewReject(ctx, userInfo.UserId)
			}
		} else {
			// 开关关闭，直接处理审核结果（从审核记录获取数据）
			if req.Result == svcreview.AuditResult_pass {
				// 查找对应的审核记录
				// 从审核记录获取语音签名信息
				content, err := record.GetContentStruct()
				if err == nil {
					err := s.userModel.UpdateUser(ctx, userInfo.UserId, map[string]any{
						"audit_status":        svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED, //关闭审核视为自动审核通过
						"updated_at":          util.NowTimeMillis(),
						"voice_signature_url": content.VoiceSignatureUrl,
						"voice_duration":      content.VoiceDuration,
					})
					if err != nil {
						logger.Errorf("UpdateUser by pass error: %v", err)
					}
				}
			} else {
				// 拒绝时不需要更新用户表，只更新审核记录状态即可
				// 发送审核未通过通知
				s.NotifyVoiceSignatureReviewReject(ctx, userInfo.UserId)
			}
		}
	}

	// 删除用户缓存
	err = s.cache.DeleteUserInfo(ctx, req.GetUserid())
	if err != nil {
		logger.Errorf("delete user info cache failed: %v", err)
	}

	return rep, nil
}

// 语音签名审核拒绝通知
func (s *AccountService) NotifyVoiceSignatureReviewReject(ctx context.Context, userId int64) {
	msgContent := map[string]any{
		"title": &svcchat.TemplateMsg{
			Tpl: "审核通知",
		},
		"content": &svcchat.TemplateMsg{
			Tpl:  fmt.Sprintf("您的语音签名未通过审核，可能包含违规内容或不适宜公开展示，请修改后重新提交"),
			Url:  "vc://voicelives/account/edit",
			Type: svcchat.TemplateJumpType_jump_user_info,
		},
		"bgm_url":  alioss.FillImageUrl(config.GetSignNotificationBg()),
		"jump_url": "vc://voicelives/account/edit",
	}

	msg := &basemsgtransfer.SendMsgReq{
		Msgs: []*basemsgtransfer.MsgData{
			{
				From:        consts.OfficialSecretaryUserid,
				To:          userId,
				SessionType: int32(basemsgtransfer.SessionType_SessionTypeSystem),
				ContentType: int32(basemsgtransfer.ContentType_RichTextNormal),
				Content:     util.JsonStr(msgContent),
				MsgFrom:     uint32(basemsgtransfer.MsgFromEnum_MsgFromIm),
				CreateTime:  util.NowTimeMillis(),
			},
		},
	}
	logger.Infof("account review notify msg=%+v", util.JsonStr(msg))
	vResp, tmpErr := svcmgr.BaseMsgTransferClient().SendMsg(ctx, msg)
	if tmpErr != nil || errcode.NotOk(vResp) {
		logger.Errorf("发送审核通知失败:err=%+v vResp=%+v", tmpErr, util.JsonStr(vResp))
	}
}

// ResetUserInfo 重置用户信息
func (s *AccountService) ResetUserInfo(ctx context.Context, userID int64) error {
	// 检查用户是否存在
	user, err := s.GetUserByUserId(ctx, userID)
	if err != nil {
		logger.Errorf("GetUserByUserId error: %v", err)
		return errcode.ErrUserNotFound
	}
	if user == nil {
		return errcode.ErrUserNotFound
	}

	// 生成重置后的昵称
	nickname := fmt.Sprintf("%s%d", config.GetDefaultNickname(), userID)

	// 准备重置的字段
	updates := map[string]any{
		"updated_at":                 util.NowTimeMillis(),
		"nickname":                   nickname,
		"nickname_pinyin":            util.ConvertToPinyin(nickname),
		"avatar":                     config.GetDefaultAvatar(),
		"background_url":             config.GetDefaultBg(),
		"voice_signature_url":        "",
		"voice_duration":             0,
		"review_voice_signature_url": "",
		"review_voice_duration":      0,
	}

	// 删除所有类型的待审核记录（因为是完全重置）
	auditTypes := []model.UserAuditType{
		model.UserAuditTypeAvatar,
		model.UserAuditTypeBackground,
		model.UserAuditTypeNickname,
		model.UserAuditTypeVoice,
	}

	for _, auditType := range auditTypes {
		err := s.userAuditModel.SoftDeletePendingAuditRecords(ctx, userID, auditType)
		if err != nil {
			logger.Errorf("删除待审核记录失败: %v, userId: %d, auditType: %d", err, userID, auditType)
			// 不影响主流程，只记录日志
		}
	}
	logger.Infof("用户信息重置，已删除所有待审核记录, userId: %d", userID)

	err = s.userModel.UpdateUser(ctx, userID, updates)
	if err != nil {
		logger.Errorf("UpdateUser for reset error: %v", err)
		return err
	}

	// 清除用户缓存，确保下次获取用户信息时能获取到最新状态
	err = s.cache.DeleteUserInfo(ctx, userID)
	if err != nil {
		logger.Errorf("delete user info cache failed: %v", err)
	}

	logger.Infof("user %d info reset successfully", userID)
	return nil
}

// BanUser 封禁用户
func (s *AccountService) BanUser(ctx context.Context, userID int64) error {
	// 检查用户是否存在
	user, err := s.GetUserByUserId(ctx, userID)
	if err != nil {
		logger.Errorf("GetUserByUserId error: %v", err)
		return errcode.ErrUserNotFound
	}
	if user == nil {
		return errcode.ErrUserNotFound
	}

	// 更新用户状态为禁用
	updates := map[string]any{
		"updated_at": util.NowTimeMillis(),
		"status":     consts.UserStatusBlock,
	}

	err = s.userModel.UpdateUser(ctx, userID, updates)
	if err != nil {
		logger.Errorf("UpdateUser for ban error: %v", err)
		return err
	}

	// 清除用户缓存
	err = s.cache.DeleteUserInfo(ctx, userID)
	if err != nil {
		logger.Errorf("delete user info cache failed: %v", err)
	}

	// 踢下线用户的所有设备
	err = s.KickOffDevice(ctx, userID, "")
	if err != nil {
		logger.Errorf("kick off user devices failed: %v", err)
	}

	logger.Infof("user %d banned successfully", userID)
	return nil
}

// UnbanUser 解封用户
func (s *AccountService) UnbanUser(ctx context.Context, userID int64) error {
	// 检查用户是否存在
	user, err := s.GetUserByUserId(ctx, userID)
	if err != nil {
		logger.Errorf("GetUserByUserId error: %v", err)
		return errcode.ErrUserNotFound
	}
	if user == nil {
		return errcode.ErrUserNotFound
	}

	// 检查用户当前状态
	if user.Status != consts.UserStatusBlock {
		logger.Warnf("user %d is not banned, current status: %d", userID, user.Status)
		return errcode.ErrUserNotBlocked
	}

	// 更新用户状态为正常
	updates := map[string]any{
		"updated_at": util.NowTimeMillis(),
		"status":     consts.UserStatusNormal,
	}

	err = s.userModel.UpdateUser(ctx, userID, updates)
	if err != nil {
		logger.Errorf("UpdateUser for unban error: %v", err)
		return err
	}

	// 清除用户缓存，确保下次获取用户信息时能获取到最新状态
	err = s.cache.DeleteUserInfo(ctx, userID)
	if err != nil {
		logger.Errorf("delete user info cache failed: %v", err)
	}

	logger.Infof("user %d unbanned successfully", userID)
	return nil
}

// createUserAuditRecord 创建用户审核记录
func (s *AccountService) createUserAuditRecord(ctx context.Context, userId int64, auditType model.UserAuditType, content *model.UserAuditContent, status svcscript.ReviewStatus) (int64, error) {
	record := &model.UserAuditRecord{
		UserId:      userId,
		AuditType:   auditType,
		AuditStatus: status,
	}

	err := record.SetContentStruct(content)
	if err != nil {
		logger.Errorf("set audit record content failed: %v", err)
		return 0, err
	}

	// 如果是待审核状态，使用替换逻辑（先删除旧的待审核记录再创建新记录）
	if status == svcscript.ReviewStatus_REVIEW_STATUS_PENDING {
		err = s.userAuditModel.CreateUserAuditRecordWithReplace(ctx, record)
	} else {
		err = s.userAuditModel.CreateUserAuditRecord(ctx, record)
	}

	if err != nil {
		logger.Errorf("create user audit record failed: %v", err)
		return 0, err
	}

	logger.Infof("created user audit record: userId=%d, type=%d, status=%d", userId, auditType, status)
	return record.ID, nil
}

// AdminUpdateUser Admin审核更新用户信息（不触发审核逻辑）
func (s *AccountService) AdminUpdateUser(ctx context.Context, userId int64, req *svcaccount.AdminUpdateUserInfoReq) error {
	userInfo, err := s.userModel.GetUserByUserId(ctx, userId)
	if err != nil {
		logger.Errorf("GetUserByUserId error: %v", err)
		return errcode.ErrUserNotFound
	}
	if userInfo == nil {
		return errcode.ErrUserNotFound
	}

	updates := map[string]any{
		"updated_at": util.NowTimeMillis(),
	}

	// 直接更新用户信息，不触发审核逻辑
	if req.Avatar != "" {
		updates["avatar"] = req.Avatar
	}
	if req.Nickname != "" {
		updates["nickname"] = req.Nickname
		// 更新昵称拼音
		updates["nickname_pinyin"] = util.ConvertToPinyin(req.Nickname)
	}
	if req.BackgroundUrl != "" {
		updates["background_url"] = req.BackgroundUrl
	}
	if req.VoiceSignatureUrl != "" {
		updates["voice_signature_url"] = req.VoiceSignatureUrl
	}
	if req.VoiceDuration > 0 {
		updates["voice_duration"] = req.VoiceDuration
	}

	if len(updates) == 1 {
		return nil
	}

	err = s.userModel.UpdateUser(ctx, userId, updates)
	if err != nil {
		logger.Errorf("AdminUpdateUser error: %v", err)
		return errcode.ErrUserUpdateFailed
	}

	// 删除用户缓存
	err = s.cache.DeleteUserInfo(ctx, userId)
	if err != nil {
		logger.Errorf("delete user info cache failed: %v", err)
	}

	logger.Infof("Admin更新用户信息成功, userId: %d, updates: %v", userId, updates)
	return nil
}

// AdminCreateUser Admin创建用户
func (s *AccountService) AdminCreateUser(ctx context.Context, req *svcaccount.AdminCreateUserReq) (int64, error) {
	// 检查手机号是否已存在
	existingUser, err := s.userModel.GetActiveUserByPhone(ctx, req.Phone)
	if err != nil && !errors.Is(err, errcode.ErrUserNotFound) {
		logger.Errorf("GetActiveUserByPhone error: %v", err)
		return 0, errcode.ErrDBQueryFailed
	}
	if existingUser != nil {
		return 0, errcode.ErrPhoneAlreadyBound
	}

	now := util.NowTimeMillis()
	user := &model.User{
		Phone:             req.Phone,
		Nickname:          req.Nickname,
		Avatar:            req.Avatar,
		Gender:            req.Gender,
		Status:            consts.UserStatus(req.Status),
		BackgroundUrl:     req.BackgroundUrl,
		VoiceSignatureUrl: req.VoiceSignatureUrl,
		VoiceDuration:     req.VoiceDuration,
		IsPremiumCreator:  req.IsPremiumCreator,
		CreationUserId:    uint(req.CreationUserId),
		CreatedAt:         now,
		UpdatedAt:         now,
	}

	// 设置默认值
	if user.Nickname == "" {
		user.Nickname = config.GetDefaultNickname()
	}
	if user.Avatar == "" {
		user.Avatar = config.GetDefaultAvatar()
	}
	if user.Status == 0 {
		user.Status = consts.UserStatusNormal
	}
	if user.BackgroundUrl == "" {
		user.BackgroundUrl = config.GetDefaultBg()
	}

	// 启动事务处理
	var createdUser *model.User
	err = s.userModel.(*model.UserModel).DB.Transaction(func(tx *gorm.DB) error {
		// 1. 创建用户基本信息
		if err := s.userModel.CreateUserWithTx(ctx, tx, user); err != nil {
			logger.Errorf("create user failed: %v", err)
			return err
		}

		// 1.1 如果昵称为默认值，更新为包含用户ID的昵称
		if req.Nickname == "" {
			nickname := fmt.Sprintf("%s%d", config.GetDefaultNickname(), user.UserId)
			if err := s.userModel.UpdateUserWithTx(ctx, tx, user.UserId, map[string]interface{}{
				"nickname":        nickname,
				"nickname_pinyin": util.ConvertToPinyin(nickname),
			}); err != nil {
				logger.Errorf("update user nickname failed: %v", err)
				return err
			}
		} else {
			// 更新昵称拼音
			if err := s.userModel.UpdateUserWithTx(ctx, tx, user.UserId, map[string]interface{}{
				"nickname_pinyin": util.ConvertToPinyin(user.Nickname),
			}); err != nil {
				logger.Errorf("update user nickname_pinyin failed: %v", err)
				return err
			}
		}

		// 2. 创建用户积分记录
		if err := s.scoreModel.AddUserScoreWithTx(ctx, tx, user.UserId, 0, 0); err != nil {
			return err
		}

		createdUser = user
		return nil
	})

	if err != nil {
		logger.Errorf("AdminCreateUser transaction failed: %v", err)
		return 0, errcode.ErrUserCreateFailed
	}

	logger.Infof("Admin创建用户成功, userId: %d, phone: %s", createdUser.UserId, createdUser.Phone)
	return createdUser.UserId, nil
}

// GetUserAuditRecordsByUserIdAndType 根据用户ID和审核类型获取审核记录
func (s *AccountService) GetUserAuditRecordsByUserIdAndType(ctx context.Context, userId int64, auditType model.UserAuditType, limit, offset int) ([]*model.UserAuditRecord, int64, error) {
	return s.userAuditModel.GetUserAuditRecordsByUserIdAndType(ctx, userId, model.UserAuditType(auditType), limit, offset)
}
